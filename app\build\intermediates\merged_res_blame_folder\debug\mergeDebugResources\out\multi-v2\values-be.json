{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\835032a51b5d40afa0ab30aa91122c7e\\transformed\\quickie-bundled-1.10.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,128", "endColumns": "72,69", "endOffsets": "123,193"}, "to": {"startLines": "209,210", "startColumns": "4,4", "startOffsets": "20415,20488", "endColumns": "72,69", "endOffsets": "20483,20553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\aaf9750c511786fa108e7a3c496402ad\\transformed\\play-services-basement-18.5.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5892", "endColumns": "145", "endOffsets": "6033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\303c2166503144bea901100ae0f040af\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,223", "endColumns": "80,86,102", "endOffsets": "131,218,321"}, "to": {"startLines": "35,226,227", "startColumns": "4,4,4", "startOffsets": "3162,21826,21913", "endColumns": "80,86,102", "endOffsets": "3238,21908,22011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4e69add8b1376897dea52ac0f7720f60\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,280,364,458,561,647,727,816,904,986,1069,1156,1228,1315,1399,1477,1553,1638,1708", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "275,359,453,556,642,722,811,899,981,1064,1151,1223,1310,1394,1472,1548,1633,1703,1826"}, "to": {"startLines": "51,52,71,72,73,78,79,206,207,211,212,216,218,219,220,221,223,224,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4711,4804,7120,7214,7317,7718,7798,20165,20253,20558,20641,20971,21122,21209,21293,21371,21548,21633,21703", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "4799,4883,7209,7312,7398,7793,7882,20248,20330,20636,20723,21038,21204,21288,21366,21442,21628,21698,21821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d2e6b12382a4dadf34d77e521366ccff\\transformed\\play-services-base-18.5.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4888,4995,5157,5282,5392,5547,5673,5788,6038,6200,6307,6470,6598,6751,6910,6979,7041", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "4990,5152,5277,5387,5542,5668,5783,5887,6195,6302,6465,6593,6746,6905,6974,7036,7115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8bf40832b048d1f4d2354372c996192d\\transformed\\material3-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,621,718,832,955,1070,1215,1299,1410,1503,1600,1714,1837,1953,2100,2246,2384,2561,2693,2818,2947,3069,3163,3261,3387,3520,3619,3730,3839,3989,4142,4250,4350,4435,4530,4626,4744,4861,4978,5064,5151,5242,5342,5440,5527,5627,5714,5814,5920,6016,6114,6203,6311,6407,6507,6653,6743,6887,7024,7142,7238,7347", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,116,116,85,86,90,99,97,86,99,86,99,105,95,97,88,107,95,99,145,89,143,136,117,95,108,110", "endOffsets": "167,282,401,518,616,713,827,950,1065,1210,1294,1405,1498,1595,1709,1832,1948,2095,2241,2379,2556,2688,2813,2942,3064,3158,3256,3382,3515,3614,3725,3834,3984,4137,4245,4345,4430,4525,4621,4739,4856,4973,5059,5146,5237,5337,5435,5522,5622,5709,5809,5915,6011,6109,6198,6306,6402,6502,6648,6738,6882,7019,7137,7233,7342,7453"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7950,8067,8182,8301,8418,8516,8613,8727,8850,8965,9110,9194,9305,9398,9495,9609,9732,9848,9995,10141,10279,10456,10588,10713,10842,10964,11058,11156,11282,11415,11514,11625,11734,11884,12037,12145,12245,12330,12425,12521,12639,12756,12873,12959,13046,13137,13237,13335,13422,13522,13609,13709,13815,13911,14009,14098,14206,14302,14402,14548,14638,14782,14919,15037,15133,15242", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,116,116,85,86,90,99,97,86,99,86,99,105,95,97,88,107,95,99,145,89,143,136,117,95,108,110", "endOffsets": "8062,8177,8296,8413,8511,8608,8722,8845,8960,9105,9189,9300,9393,9490,9604,9727,9843,9990,10136,10274,10451,10583,10708,10837,10959,11053,11151,11277,11410,11509,11620,11729,11879,12032,12140,12240,12325,12420,12516,12634,12751,12868,12954,13041,13132,13232,13330,13417,13517,13604,13704,13810,13906,14004,14093,14201,14297,14397,14543,14633,14777,14914,15032,15128,15237,15348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2f477ab85e7d6008688a1a0c4b191573\\transformed\\material-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "160", "startColumns": "4", "startOffsets": "16338", "endColumns": "90", "endOffsets": "16424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b26bc0a945bb328544f80db00c932160\\transformed\\material-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1197,1264,1357,1434,1497,1613,1676,1745,1804,1875,1934,1988,2109,2170,2233,2287,2360,2482,2570,2646,2737,2818,2901,3053,3139,3226,3359,3450,3533,3590,3641,3707,3779,3856,3927,4010,4085,4162,4244,4320,4428,4517,4599,4690,4786,4860,4941,5036,5090,5172,5238,5325,5411,5473,5537,5600,5669,5779,5892,5995,6102,6163,6218,6298,6383,6459", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,132,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79,84,75,78", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1192,1259,1352,1429,1492,1608,1671,1740,1799,1870,1929,1983,2104,2165,2228,2282,2355,2477,2565,2641,2732,2813,2896,3048,3134,3221,3354,3445,3528,3585,3636,3702,3774,3851,3922,4005,4080,4157,4239,4315,4423,4512,4594,4685,4781,4855,4936,5031,5085,5167,5233,5320,5406,5468,5532,5595,5664,5774,5887,5990,6097,6158,6213,6293,6378,6454,6533"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,74,75,76,77,80,147,148,149,150,151,152,153,154,155,156,157,158,159,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,208,214,215,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3243,3320,3397,3479,3576,4399,4496,4628,7403,7481,7548,7641,7887,15353,15469,15532,15601,15660,15731,15790,15844,15965,16026,16089,16143,16216,16429,16517,16593,16684,16765,16848,17000,17086,17173,17306,17397,17480,17537,17588,17654,17726,17803,17874,17957,18032,18109,18191,18267,18375,18464,18546,18637,18733,18807,18888,18983,19037,19119,19185,19272,19358,19420,19484,19547,19616,19726,19839,19942,20049,20110,20335,20810,20895,21043", "endLines": "7,36,37,38,39,40,48,49,50,74,75,76,77,80,147,148,149,150,151,152,153,154,155,156,157,158,159,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,208,214,215,217", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,132,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79,84,75,78", "endOffsets": "427,3315,3392,3474,3571,3663,4491,4623,4706,7476,7543,7636,7713,7945,15464,15527,15596,15655,15726,15785,15839,15960,16021,16084,16138,16211,16333,16512,16588,16679,16760,16843,16995,17081,17168,17301,17392,17475,17532,17583,17649,17721,17798,17869,17952,18027,18104,18186,18262,18370,18459,18541,18632,18728,18802,18883,18978,19032,19114,19180,19267,19353,19415,19479,19542,19611,19721,19834,19937,20044,20105,20160,20410,20890,20966,21117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\be5cc04b6a4fee62d71666ba2f889b70\\transformed\\appcompat-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,20728", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,20805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d642686678dc9e4dc645c444fc2595b8\\transformed\\core-1.16.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "41,42,43,44,45,46,47,222", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3668,3766,3868,3968,4069,4175,4278,21447", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3761,3863,3963,4064,4170,4273,4394,21543"}}]}]}