/ Header Record For PersistentHashMapValueStoragef eapp/build/generated/ksp/debug/kotlin/me/rerere/rikkahub/data/db/AppDatabase_AutoMigration_1_2_Impl.ktf eapp/build/generated/ksp/debug/kotlin/me/rerere/rikkahub/data/db/AppDatabase_AutoMigration_2_3_Impl.ktf eapp/build/generated/ksp/debug/kotlin/me/rerere/rikkahub/data/db/AppDatabase_AutoMigration_3_4_Impl.ktf eapp/build/generated/ksp/debug/kotlin/me/rerere/rikkahub/data/db/AppDatabase_AutoMigration_4_5_Impl.ktT Sapp/build/generated/ksp/debug/kotlin/me/rerere/rikkahub/data/db/AppDatabase_Impl.kt\ [app/build/generated/ksp/debug/kotlin/me/rerere/rikkahub/data/db/dao/ConversationDAO_Impl.ktV Uapp/build/generated/ksp/debug/kotlin/me/rerere/rikkahub/data/db/dao/MemoryDAO_Impl.kt4 3app/src/main/java/me/rerere/rikkahub/RikkaHubApp.kt6 5app/src/main/java/me/rerere/rikkahub/RouteActivity.ktR Qapp/src/main/java/me/rerere/rikkahub/data/ai/Base64ImageToLocalFileTransformer.ktB Aapp/src/main/java/me/rerere/rikkahub/data/ai/GenerationHandler.kt= <app/src/main/java/me/rerere/rikkahub/data/api/RikkaHubAPI.ktH Gapp/src/main/java/me/rerere/rikkahub/data/datastore/PreferencesStore.kt< ;app/src/main/java/me/rerere/rikkahub/data/db/AppDatabase.ktD Capp/src/main/java/me/rerere/rikkahub/data/db/dao/ConversationDAO.kt> =app/src/main/java/me/rerere/rikkahub/data/db/dao/MemoryDAO.ktJ Iapp/src/main/java/me/rerere/rikkahub/data/db/entity/ConversationEntity.ktD Capp/src/main/java/me/rerere/rikkahub/data/db/entity/MemoryEntity.kt; :app/src/main/java/me/rerere/rikkahub/data/mcp/McpConfig.kt< ;app/src/main/java/me/rerere/rikkahub/data/mcp/McpManager.ktN Mapp/src/main/java/me/rerere/rikkahub/data/mcp/transport/SseClientTransport.kt= <app/src/main/java/me/rerere/rikkahub/data/model/Assistant.kt@ ?app/src/main/java/me/rerere/rikkahub/data/model/Conversation.kt? >app/src/main/java/me/rerere/rikkahub/data/model/Leaderboard.ktO Napp/src/main/java/me/rerere/rikkahub/data/repository/ConversationRepository.ktI Happ/src/main/java/me/rerere/rikkahub/data/repository/MemoryRepository.kt5 4app/src/main/java/me/rerere/rikkahub/di/AppModule.kt< ;app/src/main/java/me/rerere/rikkahub/di/DataSourceModule.kt< ;app/src/main/java/me/rerere/rikkahub/di/RepositoryModule.kt; :app/src/main/java/me/rerere/rikkahub/di/ViewModelModule.kt= <app/src/main/java/me/rerere/rikkahub/services/ChatService.ktK Japp/src/main/java/me/rerere/rikkahub/ui/components/chat/AssistantPicker.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/components/chat/ChatInput.ktG Fapp/src/main/java/me/rerere/rikkahub/ui/components/chat/ChatMessage.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/components/chat/ModelList.ktL Kapp/src/main/java/me/rerere/rikkahub/ui/components/chat/SearchResultList.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/components/nav/BackButton.ktR Qapp/src/main/java/me/rerere/rikkahub/ui/components/richtext/HighlightCodeBlock.ktI Happ/src/main/java/me/rerere/rikkahub/ui/components/richtext/LatexText.ktH Gapp/src/main/java/me/rerere/rikkahub/ui/components/richtext/Markdown.ktI Happ/src/main/java/me/rerere/rikkahub/ui/components/richtext/MathBlock.ktG Fapp/src/main/java/me/rerere/rikkahub/ui/components/richtext/Mermaid.ktR Qapp/src/main/java/me/rerere/rikkahub/ui/components/richtext/ZoomableAsyncImage.ktF Eapp/src/main/java/me/rerere/rikkahub/ui/components/table/DataTable.ktH Gapp/src/main/java/me/rerere/rikkahub/ui/components/table/TableColumn.kt@ ?app/src/main/java/me/rerere/rikkahub/ui/components/ui/AIIcon.kt@ ?app/src/main/java/me/rerere/rikkahub/ui/components/ui/Avatar.ktA @app/src/main/java/me/rerere/rikkahub/ui/components/ui/Favicon.kt> =app/src/main/java/me/rerere/rikkahub/ui/components/ui/Form.ktL Kapp/src/main/java/me/rerere/rikkahub/ui/components/ui/ImagePreviewDialog.kt? >app/src/main/java/me/rerere/rikkahub/ui/components/ui/Input.ktF Eapp/src/main/java/me/rerere/rikkahub/ui/components/ui/KeepScreenOn.ktL Kapp/src/main/java/me/rerere/rikkahub/ui/components/ui/ListSelectableItem.kt@ ?app/src/main/java/me/rerere/rikkahub/ui/components/ui/QRCode.kt@ ?app/src/main/java/me/rerere/rikkahub/ui/components/ui/Select.ktD Capp/src/main/java/me/rerere/rikkahub/ui/components/ui/ShareSheet.kt= <app/src/main/java/me/rerere/rikkahub/ui/components/ui/Tag.ktO Napp/src/main/java/me/rerere/rikkahub/ui/components/ui/WavyProgressIndicator.ktK Japp/src/main/java/me/rerere/rikkahub/ui/components/ui/icons/DiscordIcon.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/components/ui/icons/Heart.ktM Lapp/src/main/java/me/rerere/rikkahub/ui/components/ui/icons/TencentQQIcon.ktF Eapp/src/main/java/me/rerere/rikkahub/ui/components/webview/WebView.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/context/FirebaseAnalytics.ktA @app/src/main/java/me/rerere/rikkahub/ui/context/LocalSettings.kt> =app/src/main/java/me/rerere/rikkahub/ui/context/NavContext.ktA @app/src/main/java/me/rerere/rikkahub/ui/context/SharedElement.ktB Aapp/src/main/java/me/rerere/rikkahub/ui/context/ToasterContext.kt; :app/src/main/java/me/rerere/rikkahub/ui/hooks/ColorMode.kt: 9app/src/main/java/me/rerere/rikkahub/ui/hooks/Debounce.kt? >app/src/main/java/me/rerere/rikkahub/ui/hooks/HeroAnimation.kt; :app/src/main/java/me/rerere/rikkahub/ui/hooks/Lifecycle.kt: 9app/src/main/java/me/rerere/rikkahub/ui/hooks/Settings.ktC Bapp/src/main/java/me/rerere/rikkahub/ui/hooks/SharedPreferences.kt> =app/src/main/java/me/rerere/rikkahub/ui/hooks/UseAssistant.kt> =app/src/main/java/me/rerere/rikkahub/ui/hooks/UseEditState.kt> =app/src/main/java/me/rerere/rikkahub/ui/hooks/tts/TtsState.kt< ;app/src/main/java/me/rerere/rikkahub/ui/modifier/Shimmer.ktI Happ/src/main/java/me/rerere/rikkahub/ui/pages/assistant/AssistantPage.ktG Fapp/src/main/java/me/rerere/rikkahub/ui/pages/assistant/AssistantVM.ktV Uapp/src/main/java/me/rerere/rikkahub/ui/pages/assistant/detail/AssistantDetailPage.ktT Sapp/src/main/java/me/rerere/rikkahub/ui/pages/assistant/detail/AssistantDetailVM.ktQ Papp/src/main/java/me/rerere/rikkahub/ui/pages/assistant/detail/PropertyEditor.kt? >app/src/main/java/me/rerere/rikkahub/ui/pages/chat/ChatPage.kt= <app/src/main/java/me/rerere/rikkahub/ui/pages/chat/ChatVM.ktG Fapp/src/main/java/me/rerere/rikkahub/ui/pages/chat/ConversationList.kt= <app/src/main/java/me/rerere/rikkahub/ui/pages/chat/Export.ktA @app/src/main/java/me/rerere/rikkahub/ui/pages/debug/DebugPage.kt? >app/src/main/java/me/rerere/rikkahub/ui/pages/debug/DebugVM.kt; :app/src/main/java/me/rerere/rikkahub/ui/pages/debug/Tts.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/pages/history/HistoryPage.ktC Bapp/src/main/java/me/rerere/rikkahub/ui/pages/history/HistoryVM.kt? >app/src/main/java/me/rerere/rikkahub/ui/pages/menu/MenuPage.ktJ Iapp/src/main/java/me/rerere/rikkahub/ui/pages/setting/SettingAboutPage.ktL Kapp/src/main/java/me/rerere/rikkahub/ui/pages/setting/SettingDisplayPage.ktH Gapp/src/main/java/me/rerere/rikkahub/ui/pages/setting/SettingMcpPage.ktJ Iapp/src/main/java/me/rerere/rikkahub/ui/pages/setting/SettingModelPage.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/pages/setting/SettingPage.ktM Lapp/src/main/java/me/rerere/rikkahub/ui/pages/setting/SettingProviderPage.ktK Japp/src/main/java/me/rerere/rikkahub/ui/pages/setting/SettingSearchPage.ktC Bapp/src/main/java/me/rerere/rikkahub/ui/pages/setting/SettingVM.ktV Uapp/src/main/java/me/rerere/rikkahub/ui/pages/setting/components/PresetThemeButton.ktV Uapp/src/main/java/me/rerere/rikkahub/ui/pages/setting/components/ProviderConfigure.ktK Japp/src/main/java/me/rerere/rikkahub/ui/pages/translator/TranslatorPage.ktI Happ/src/main/java/me/rerere/rikkahub/ui/pages/translator/TranslatorVM.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/pages/webview/WebViewPage.kt; :app/src/main/java/me/rerere/rikkahub/ui/theme/CodeColor.kt7 6app/src/main/java/me/rerere/rikkahub/ui/theme/Color.kt= <app/src/main/java/me/rerere/rikkahub/ui/theme/PresetTheme.kt7 6app/src/main/java/me/rerere/rikkahub/ui/theme/Theme.kt6 5app/src/main/java/me/rerere/rikkahub/ui/theme/Type.ktD Capp/src/main/java/me/rerere/rikkahub/ui/theme/presets/BlackTheme.ktD Capp/src/main/java/me/rerere/rikkahub/ui/theme/presets/OceanTheme.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/theme/presets/SakuraTheme.ktE Dapp/src/main/java/me/rerere/rikkahub/ui/theme/presets/SpringTheme.kt7 6app/src/main/java/me/rerere/rikkahub/utils/ChatUtil.kt< ;app/src/main/java/me/rerere/rikkahub/utils/ClipboardUtil.kt> =app/src/main/java/me/rerere/rikkahub/utils/CollectionUtils.kt9 8app/src/main/java/me/rerere/rikkahub/utils/ComposeExt.kt: 9app/src/main/java/me/rerere/rikkahub/utils/ContextUtil.kt= <app/src/main/java/me/rerere/rikkahub/utils/CoroutineUtils.kt3 2app/src/main/java/me/rerere/rikkahub/utils/Json.kt: 9app/src/main/java/me/rerere/rikkahub/utils/StringUtils.kt7 6app/src/main/java/me/rerere/rikkahub/utils/TimeUtil.kt6 5app/src/main/java/me/rerere/rikkahub/utils/UiState.kt< ;app/src/main/java/me/rerere/rikkahub/utils/UpdateChecker.kt