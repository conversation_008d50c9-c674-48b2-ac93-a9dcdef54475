{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b48892203169544dad7e4ce4c2aaf9a0\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4048,4142,4234,4317,4399,4484,4573,4663,4743,4835,4917,5014,5108,5201,5294,5378,5475,5571,5666,5774,5854,5962,6065,6159,6251,6350", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,93,91,82,81,84,88,89,79,91,81,96,93,92,92,83,96,95,94,107,79,107,102,93,91,98,98", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4043,4137,4229,4312,4394,4479,4568,4658,4738,4830,4912,5009,5103,5196,5289,5373,5470,5566,5661,5769,5849,5957,6060,6154,6246,6345,6444"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12300,12404,12507,12611,12713,12805,12893,12997,13102,13207,13323,13405,13501,13585,13673,13778,13891,13992,14101,14208,14316,14433,14538,14639,14743,14848,14933,15028,15133,15242,15332,15432,15530,15641,15757,15857,15948,16022,16112,16201,16293,16387,16479,16562,16644,16729,16818,16908,16988,17080,17162,17259,17353,17446,17539,17623,17720,17816,17911,18019,18099,18207,18310,18404,18496,18595", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,93,91,82,81,84,88,89,79,91,81,96,93,92,92,83,96,95,94,107,79,107,102,93,91,98,98", "endOffsets": "12399,12502,12606,12708,12800,12888,12992,13097,13202,13318,13400,13496,13580,13668,13773,13886,13987,14096,14203,14311,14428,14533,14634,14738,14843,14928,15023,15128,15237,15327,15427,15525,15636,15752,15852,15943,16017,16107,16196,16288,16382,16474,16557,16639,16724,16813,16903,16983,17075,17157,17254,17348,17441,17534,17618,17715,17811,17906,18014,18094,18202,18305,18399,18491,18590,18689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9436b4ebc37cac3d97710224e74d8985\\transformed\\play-services-base-18.5.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "133,134,135,136,137,138,139,140,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9604,9705,9833,9948,10050,10157,10273,10373,10571,10681,10782,10911,11026,11128,11236,11292,11349", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "9700,9828,9943,10045,10152,10268,10368,10463,10676,10777,10906,11021,11123,11231,11287,11344,11418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\757e326fd7991b30514ac12e75a7701a\\transformed\\appcompat-1.7.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,316", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,24403", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,24477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b9676b1ec2d82f1cef53d671c6d0afde\\transformed\\material-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "244", "startColumns": "4", "startOffsets": "19529", "endColumns": "84", "endOffsets": "19609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\ec01753cc085eccabdf318240ae84988\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,254,330,417,508,586,660,737,815,890,963,1038,1106,1187,1260,1332,1403,1477,1545", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,80,72,71,70,73,67,115", "endOffsets": "249,325,412,503,581,655,732,810,885,958,1033,1101,1182,1255,1327,1398,1472,1540,1656"}, "to": {"startLines": "129,130,153,154,155,162,163,306,307,313,314,319,368,369,370,371,374,375,377", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9352,9429,11506,11593,11684,12089,12163,23768,23846,24211,24284,24630,27888,27969,28042,28114,28322,28396,28508", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,80,72,71,70,73,67,115", "endOffsets": "9424,9500,11588,11679,11757,12158,12235,23841,23916,24279,24354,24693,27964,28037,28109,28180,28391,28459,28619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9d7219de08abf79892e9add9bb5b00cd\\transformed\\material-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,912,974,1052,1112,1172,1250,1311,1369,1425,1485,1543,1597,1682,1738,1796,1850,1915,2007,2081,2153,2235,2309,2386,2506,2569,2632,2731,2808,2882,2932,2983,3049,3112,3180,3251,3322,3383,3454,3521,3583,3670,3749,3814,3897,3982,4056,4120,4196,4244,4317,4381,4457,4535,4597,4661,4724,4790,4870,4948,5024,5103,5157,5212,5281,5356,5429", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "242,306,368,435,505,582,676,783,856,907,969,1047,1107,1167,1245,1306,1364,1420,1480,1538,1592,1677,1733,1791,1845,1910,2002,2076,2148,2230,2304,2381,2501,2564,2627,2726,2803,2877,2927,2978,3044,3107,3175,3246,3317,3378,3449,3516,3578,3665,3744,3809,3892,3977,4051,4115,4191,4239,4312,4376,4452,4530,4592,4656,4719,4785,4865,4943,5019,5098,5152,5207,5276,5351,5424,5494"}, "to": {"startLines": "2,87,88,89,90,91,101,102,128,158,159,160,161,164,231,232,233,234,235,236,237,238,239,240,241,242,243,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,309,317,318,367", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,6516,6580,6642,6709,6779,7609,7703,9279,11838,11889,11951,12029,12240,18694,18772,18833,18891,18947,19007,19065,19119,19204,19260,19318,19372,19437,20563,20637,20709,20791,20865,20942,21062,21125,21188,21287,21364,21438,21488,21539,21605,21668,21736,21807,21878,21939,22010,22077,22139,22226,22305,22370,22453,22538,22612,22676,22752,22800,22873,22937,23013,23091,23153,23217,23280,23346,23426,23504,23580,23659,23713,23990,24482,24557,27818", "endLines": "5,87,88,89,90,91,101,102,128,158,159,160,161,164,231,232,233,234,235,236,237,238,239,240,241,242,243,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,309,317,318,367", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "292,6575,6637,6704,6774,6851,7698,7805,9347,11884,11946,12024,12084,12295,18767,18828,18886,18942,19002,19060,19114,19199,19255,19313,19367,19432,19524,20632,20704,20786,20860,20937,21057,21120,21183,21282,21359,21433,21483,21534,21600,21663,21731,21802,21873,21934,22005,22072,22134,22221,22300,22365,22448,22533,22607,22671,22747,22795,22868,22932,23008,23086,23148,23212,23275,23341,23421,23499,23575,23654,23708,23763,24054,24552,24625,27883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f58a4462c0f661bc016516b04b05ffbf\\transformed\\core-1.16.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "93,94,95,96,97,98,99,372", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6903,6995,7094,7188,7282,7375,7468,28185", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "6990,7089,7183,7277,7370,7463,7559,28281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\e744a31c82164d30026af487ba5e43bf\\transformed\\uCrop-n-Edit-4.1.0-non-native\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,97,174,228,282,329,446,490", "endColumns": "41,76,53,53,46,116,43,42", "endOffsets": "92,169,223,277,324,441,485,528"}, "to": {"startLines": "393,394,395,396,397,398,399,400", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "29514,29556,29633,29687,29741,29788,29905,29949", "endColumns": "41,76,53,53,46,116,43,42", "endOffsets": "29551,29628,29682,29736,29783,29900,29944,29987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c0ee106e096d6fe0bd1735cc2a0a4c89\\transformed\\quickie-bundled-1.10.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,110", "endColumns": "54,59", "endOffsets": "105,165"}, "to": {"startLines": "311,312", "startColumns": "4,4", "startOffsets": "24096,24151", "endColumns": "54,59", "endOffsets": "24146,24206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\96abb1e1502d74c75d73c35e14f81e22\\transformed\\play-services-basement-18.5.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "10468", "endColumns": "102", "endOffsets": "10566"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "2,93,138,132,96,99,134,135,110,101,106,105,104,100,133,128,114,113,137,131,129,130,119,120,136,109,112,107,116,108,94,117,111,201,202,98,95,197,199,198,200,97,115,121,124,122,123,125,92,102,118,103,18,66,5,3,34,38,27,26,29,32,36,42,43,44,46,45,30,35,23,22,28,39,33,31,37,24,25,17,171,172,12,16,14,4,156,159,157,160,161,155,158,175,177,176,167,168,166,165,164,8,141,10,13,7,185,184,181,180,183,182,187,186,188,77,76,78,79,62,63,60,61,64,65,191,194,193,192,73,72,71,54,55,48,49,50,51,52,53,70,56,57,58,59,67,68,69,146,145,144,19,6,11,149,151,150,152,89,84,83,85,86,87,82,88,15,9", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,4875,7976,7579,5044,5230,7711,7773,6049,5342,5735,5610,5537,5286,7644,7308,6278,6224,7912,7511,7379,7445,6612,6683,7842,5980,6155,5813,6414,5867,4926,6478,6103,11064,11127,5176,4980,10826,10944,10885,11002,5117,6345,6800,7049,6865,6973,7120,4820,5402,6546,5458,765,3436,207,104,1569,1804,1159,1087,1304,1467,1670,1952,2010,2074,2214,2151,1353,1618,932,869,1241,1863,1522,1405,1739,987,1035,711,9445,9495,508,663,590,166,8701,8922,8775,8980,9044,8629,8847,9578,9682,9625,9317,9365,9265,9195,9137,327,8066,425,545,290,10210,10124,9861,9775,10037,9954,10375,10301,10454,4054,3995,4118,4185,3184,3235,3058,3113,3295,3357,10542,10727,10665,10600,3908,3832,3763,2672,2733,2277,2340,2410,2471,2541,2605,3700,2806,2862,2927,2989,3484,3535,3598,8302,8229,8164,802,253,463,8401,8502,8451,8552,4734,4401,4340,4477,4534,4609,4286,4676,627,374", "endColumns": "45,49,59,63,71,54,60,67,52,58,76,123,71,54,65,69,65,52,62,66,64,64,69,115,68,67,67,52,62,111,52,66,50,61,73,52,62,57,56,57,60,57,67,63,69,106,74,144,53,54,64,77,35,46,44,60,47,57,80,70,47,53,67,56,62,75,59,61,50,50,53,61,61,59,45,60,63,46,50,52,48,49,35,46,35,39,72,56,70,62,60,70,73,45,54,55,46,51,50,68,56,45,68,36,43,35,89,84,91,84,85,81,77,72,59,62,57,65,67,49,58,53,69,60,77,56,59,60,63,50,74,67,59,71,61,68,59,68,62,65,61,54,63,60,67,49,61,100,69,71,63,39,35,43,48,48,49,49,53,74,59,55,73,65,52,56,34,49", "endOffsets": "98,4920,8031,7638,5111,5280,7767,7836,6097,5396,5807,5729,5604,5336,7705,7373,6339,6272,7970,7573,7439,7505,6677,6794,7906,6043,6218,5861,6472,5974,4974,6540,6149,11121,11196,5224,5038,10879,10996,10938,11058,5170,6408,6859,7114,6967,7043,7260,4869,5452,6606,5531,796,3478,247,160,1612,1857,1235,1153,1347,1516,1733,2004,2068,2145,2269,2208,1399,1664,981,926,1298,1918,1563,1461,1798,1029,1081,759,9489,9540,539,705,621,201,8769,8974,8841,9038,9100,8695,8916,9619,9732,9676,9359,9412,9311,9259,9189,368,8130,457,584,321,10295,10204,9948,9855,10118,10031,10448,10369,10509,4112,4048,4179,4248,3229,3289,3107,3178,3351,3430,10594,10782,10721,10659,3954,3902,3826,2727,2800,2334,2404,2465,2535,2599,2666,3757,2856,2921,2983,3052,3529,3592,3694,8367,8296,8223,837,284,502,8445,8546,8496,8597,4783,4471,4395,4528,4603,4670,4334,4728,657,419"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,86,92,100,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,131,132,151,152,156,157,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,308,310,315,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,373,376,378,379,380,381,384,385,386,387,388,389,390,391,392,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2909,2959,3019,3083,3155,3210,3271,3339,3392,3451,3528,3652,3724,3779,3845,3915,3981,4034,4097,4164,4229,4294,4364,4480,4549,4617,4685,4738,4801,4913,4966,5033,5084,5146,5220,5273,5336,5394,5451,5509,5570,5628,5696,5760,5830,5937,6012,6157,6211,6266,6331,6480,6856,7564,7810,7871,7919,7977,8058,8129,8177,8231,8299,8356,8419,8495,8555,8617,8668,8719,8773,8835,8897,8957,9003,9064,9128,9175,9226,9505,9554,11423,11459,11762,11798,19614,19687,19744,19815,19878,19939,20010,20084,20130,20185,20241,20288,20340,20391,20460,20517,23921,24059,24359,24698,24734,24824,24909,25001,25086,25172,25254,25332,25405,25465,25528,25586,25652,25720,25770,25829,25883,25953,26014,26092,26149,26209,26270,26334,26385,26460,26528,26588,26660,26722,26791,26851,26920,26983,27049,27111,27166,27230,27291,27359,27409,27471,27572,27642,27714,27778,28286,28464,28624,28673,28722,28772,28984,29038,29113,29173,29229,29303,29369,29422,29479,29992", "endColumns": "45,49,59,63,71,54,60,67,52,58,76,123,71,54,65,69,65,52,62,66,64,64,69,115,68,67,67,52,62,111,52,66,50,61,73,52,62,57,56,57,60,57,67,63,69,106,74,144,53,54,64,77,35,46,44,60,47,57,80,70,47,53,67,56,62,75,59,61,50,50,53,61,61,59,45,60,63,46,50,52,48,49,35,46,35,39,72,56,70,62,60,70,73,45,54,55,46,51,50,68,56,45,68,36,43,35,89,84,91,84,85,81,77,72,59,62,57,65,67,49,58,53,69,60,77,56,59,60,63,50,74,67,59,71,61,68,59,68,62,65,61,54,63,60,67,49,61,100,69,71,63,39,35,43,48,48,49,49,53,74,59,55,73,65,52,56,34,49", "endOffsets": "2904,2954,3014,3078,3150,3205,3266,3334,3387,3446,3523,3647,3719,3774,3840,3910,3976,4029,4092,4159,4224,4289,4359,4475,4544,4612,4680,4733,4796,4908,4961,5028,5079,5141,5215,5268,5331,5389,5446,5504,5565,5623,5691,5755,5825,5932,6007,6152,6206,6261,6326,6404,6511,6898,7604,7866,7914,7972,8053,8124,8172,8226,8294,8351,8414,8490,8550,8612,8663,8714,8768,8830,8892,8952,8998,9059,9123,9170,9221,9274,9549,9599,11454,11501,11793,11833,19682,19739,19810,19873,19934,20005,20079,20125,20180,20236,20283,20335,20386,20455,20512,20558,23985,24091,24398,24729,24819,24904,24996,25081,25167,25249,25327,25400,25460,25523,25581,25647,25715,25765,25824,25878,25948,26009,26087,26144,26204,26265,26329,26380,26455,26523,26583,26655,26717,26786,26846,26915,26978,27044,27106,27161,27225,27286,27354,27404,27466,27567,27637,27709,27773,27813,28317,28503,28668,28717,28767,28817,29033,29108,29168,29224,29298,29364,29417,29474,29509,30037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\bd9e012c025b7c80daa2bbfa6a34ffed\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,209", "endColumns": "70,82,78", "endOffsets": "121,204,283"}, "to": {"startLines": "85,382,383", "startColumns": "4,4,4", "startOffsets": "6409,28822,28905", "endColumns": "70,82,78", "endOffsets": "6475,28900,28979"}}]}]}