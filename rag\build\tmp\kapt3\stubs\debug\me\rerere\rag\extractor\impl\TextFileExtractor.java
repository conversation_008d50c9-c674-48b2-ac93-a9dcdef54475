package me.rerere.rag.extractor.impl;

/**
 * 从文本文件中提取文本内容
 * 支持常见的文本文件格式
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0016\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\u0002H\u0016\u00a8\u0006\t"}, d2 = {"Lme/rerere/rag/extractor/impl/TextFileExtractor;", "Lme/rerere/rag/extractor/DataExtractor;", "Ljava/io/File;", "<init>", "()V", "extract", "", "", "data", "rag_debug"})
public final class TextFileExtractor implements me.rerere.rag.extractor.DataExtractor<java.io.File> {
    
    public TextFileExtractor() {
        super();
    }
    
    /**
     * 从文件中提取文本内容
     * @param data 文本文件
     * @return 提取出的文本内容
     */
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.util.List<java.lang.String> extract(@org.jetbrains.annotations.NotNull()
    java.io.File data) {
        return null;
    }
}