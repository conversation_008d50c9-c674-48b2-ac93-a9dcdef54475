kotlin.Enum2kotlinx.serialization.internal.GeneratedSerializerme.rerere.ai.core.Schema%me.rerere.ai.provider.ProviderSettingme.rerere.ai.provider.Providerme.rerere.ai.ui.UIMessagePart#me.rerere.ai.ui.UIMessageAnnotation"me.rerere.ai.ui.MessageTransformer'me.rerere.ai.ui.InputMessageTransformer(me.rerere.ai.ui.OutputMessageTransformerjava.lang.RuntimeException!kotlinx.serialization.KSerializer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                