{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-zh/values-zh.xml", "map": [{"source": "D:\\FromTX3\\MyCode\\rikkahub\\search\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-zh\\values-zh.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,123", "endColumns": "67,60", "endOffsets": "118,179"}, "to": {"startLines": "55,83", "startColumns": "4,4", "startOffsets": "3637,5266", "endColumns": "67,60", "endOffsets": "3700,5322"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "2,93,138,132,96,99,134,135,110,101,106,105,104,100,133,128,114,113,137,131,129,130,119,120,136,109,112,107,116,108,94,117,111,201,202,98,95,197,199,198,200,97,115,121,124,122,123,125,92,102,118,103,18,66,5,3,34,38,27,26,29,32,36,42,43,44,46,45,30,35,23,22,28,39,33,31,37,24,25,17,171,172,12,16,14,4,156,159,157,160,161,155,158,175,177,176,167,168,166,165,164,8,141,10,13,7,185,184,181,180,183,182,187,186,188,77,76,78,79,62,63,60,61,64,65,191,194,193,192,73,72,71,54,55,48,49,50,51,52,53,70,56,57,58,59,67,68,69,146,145,144,19,6,11,149,151,150,152,89,84,83,85,86,87,82,88,15,9", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,4875,7976,7579,5044,5230,7711,7773,6049,5342,5735,5610,5537,5286,7644,7308,6278,6224,7912,7511,7379,7445,6612,6683,7842,5980,6155,5813,6414,5867,4926,6478,6103,11065,11128,5176,4980,10827,10945,10886,11003,5117,6345,6800,7049,6865,6973,7120,4820,5402,6546,5458,765,3436,207,104,1569,1804,1159,1087,1304,1467,1670,1952,2010,2074,2214,2151,1353,1618,932,869,1241,1863,1522,1405,1739,987,1035,711,9446,9496,508,663,590,166,8701,8922,8775,8980,9044,8629,8847,9579,9683,9626,9318,9366,9266,9195,9137,327,8066,425,545,290,10211,10125,9862,9776,10038,9955,10376,10302,10455,4054,3995,4118,4185,3184,3235,3058,3113,3295,3357,10543,10728,10666,10601,3908,3832,3763,2672,2733,2277,2340,2410,2471,2541,2605,3700,2806,2862,2927,2989,3484,3535,3598,8302,8229,8164,802,253,463,8401,8502,8451,8552,4734,4401,4340,4477,4534,4609,4286,4676,627,374", "endColumns": "45,49,59,63,71,54,60,67,52,58,76,123,71,54,65,69,65,52,62,66,64,64,69,115,68,67,67,52,62,111,52,66,50,61,73,52,62,57,56,57,60,57,67,63,69,106,74,144,53,54,64,77,35,46,44,60,47,57,80,70,47,53,67,56,62,75,59,61,50,50,53,61,61,59,45,60,63,46,50,52,48,49,35,46,35,39,72,56,70,62,60,70,73,45,54,55,46,51,50,69,56,45,68,36,43,35,89,84,91,84,85,81,77,72,59,62,57,65,67,49,58,53,69,60,77,56,59,60,63,50,74,67,59,71,61,68,59,68,62,65,61,54,63,60,67,49,61,100,69,71,63,39,35,43,48,48,49,49,53,74,59,55,73,65,52,56,34,49", "endOffsets": "98,4920,8031,7638,5111,5280,7767,7836,6097,5396,5807,5729,5604,5336,7705,7373,6339,6272,7970,7573,7439,7505,6677,6794,7906,6043,6218,5861,6472,5974,4974,6540,6149,11122,11197,5224,5038,10880,10997,10939,11059,5170,6408,6859,7114,6967,7043,7260,4869,5452,6606,5531,796,3478,247,160,1612,1857,1235,1153,1347,1516,1733,2004,2068,2145,2269,2208,1399,1664,981,926,1298,1918,1563,1461,1798,1029,1081,759,9490,9541,539,705,621,201,8769,8974,8841,9038,9100,8695,8916,9620,9733,9677,9360,9413,9312,9260,9189,368,8130,457,584,321,10296,10205,9949,9856,10119,10032,10449,10370,10510,4112,4048,4179,4248,3229,3289,3107,3178,3351,3430,10595,10783,10722,10660,3954,3902,3826,2727,2800,2334,2404,2465,2535,2599,2666,3757,2856,2921,2983,3052,3529,3592,3694,8367,8296,8223,837,284,502,8445,8546,8496,8597,4783,4471,4395,4528,4603,4670,4334,4728,657,419"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,101,151,211,275,347,402,463,531,584,643,720,844,916,971,1037,1107,1173,1226,1289,1356,1421,1486,1556,1672,1741,1809,1877,1930,1993,2105,2158,2225,2276,2338,2412,2465,2528,2586,2643,2701,2762,2820,2888,2952,3022,3129,3204,3349,3403,3458,3523,3601,3705,3752,3797,3858,3906,3964,4045,4116,4164,4218,4286,4343,4406,4482,4542,4604,4655,4706,4760,4822,4884,4944,4990,5051,5115,5162,5213,5327,5376,5426,5462,5509,5545,5585,5658,5715,5786,5849,5910,5981,6055,6101,6156,6212,6259,6311,6362,6432,6489,6535,6604,6641,6685,6721,6811,6896,6988,7073,7159,7241,7319,7392,7452,7515,7573,7639,7707,7757,7816,7870,7940,8001,8079,8136,8196,8257,8321,8372,8447,8515,8575,8647,8709,8778,8838,8907,8970,9036,9098,9153,9217,9278,9346,9396,9458,9559,9629,9701,9765,9805,9841,9885,9934,9983,10033,10083,10137,10212,10272,10328,10402,10468,10521,10578,10897", "endColumns": "45,49,59,63,71,54,60,67,52,58,76,123,71,54,65,69,65,52,62,66,64,64,69,115,68,67,67,52,62,111,52,66,50,61,73,52,62,57,56,57,60,57,67,63,69,106,74,144,53,54,64,77,35,46,44,60,47,57,80,70,47,53,67,56,62,75,59,61,50,50,53,61,61,59,45,60,63,46,50,52,48,49,35,46,35,39,72,56,70,62,60,70,73,45,54,55,46,51,50,69,56,45,68,36,43,35,89,84,91,84,85,81,77,72,59,62,57,65,67,49,58,53,69,60,77,56,59,60,63,50,74,67,59,71,61,68,59,68,62,65,61,54,63,60,67,49,61,100,69,71,63,39,35,43,48,48,49,49,53,74,59,55,73,65,52,56,34,49", "endOffsets": "96,146,206,270,342,397,458,526,579,638,715,839,911,966,1032,1102,1168,1221,1284,1351,1416,1481,1551,1667,1736,1804,1872,1925,1988,2100,2153,2220,2271,2333,2407,2460,2523,2581,2638,2696,2757,2815,2883,2947,3017,3124,3199,3344,3398,3453,3518,3596,3632,3747,3792,3853,3901,3959,4040,4111,4159,4213,4281,4338,4401,4477,4537,4599,4650,4701,4755,4817,4879,4939,4985,5046,5110,5157,5208,5261,5371,5421,5457,5504,5540,5580,5653,5710,5781,5844,5905,5976,6050,6096,6151,6207,6254,6306,6357,6427,6484,6530,6599,6636,6680,6716,6806,6891,6983,7068,7154,7236,7314,7387,7447,7510,7568,7634,7702,7752,7811,7865,7935,7996,8074,8131,8191,8252,8316,8367,8442,8510,8570,8642,8704,8773,8833,8902,8965,9031,9093,9148,9212,9273,9341,9391,9453,9554,9624,9696,9760,9800,9836,9880,9929,9978,10028,10078,10132,10207,10267,10323,10397,10463,10516,10573,10608,10942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5aea9c81c7615102d7b5640b77daf851\\transformed\\uCrop-n-Edit-4.1.0-non-native\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,97,151,205,252,296", "endColumns": "41,53,53,46,43,42", "endOffsets": "92,146,200,247,291,334"}, "to": {"startLines": "171,172,173,174,175,176", "startColumns": "4,4,4,4,4,4", "startOffsets": "10613,10655,10709,10763,10810,10854", "endColumns": "41,53,53,46,43,42", "endOffsets": "10650,10704,10758,10805,10849,10892"}}]}]}