!androidx.room.migration.Migration&me.rerere.rikkahub.data.db.AppDatabase.me.rerere.rikkahub.data.db.dao.ConversationDAO(me.rerere.rikkahub.data.db.dao.MemoryDAOandroid.app.Application!kotlinx.coroutines.CoroutineScope#androidx.activity.ComponentActivity(me.rerere.ai.ui.OutputMessageTransformer*me.rerere.rikkahub.data.ai.GenerationChunk2kotlinx.serialization.internal.GeneratedSerializerandroidx.room.RoomDatabase+me.rerere.rikkahub.data.mcp.McpServerConfig;io.modelcontextprotocol.kotlin.sdk.shared.AbstractTransportandroid.app.Serviceandroid.os.Binder'androidx.compose.runtime.saveable.Saverkotlin.Enum3androidx.compose.ui.text.input.VisualTransformation2me.rerere.rikkahub.ui.components.table.ColumnWidthandroid.webkit.WebChromeClientandroid.webkit.WebViewClient3me.rerere.rikkahub.ui.components.webview.WebContent(me.rerere.rikkahub.ui.hooks.tts.TtsState.android.speech.tts.TextToSpeech.OnInitListenerandroidx.lifecycle.ViewModel me.rerere.rikkahub.utils.UiStatekotlin.Comparable                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              