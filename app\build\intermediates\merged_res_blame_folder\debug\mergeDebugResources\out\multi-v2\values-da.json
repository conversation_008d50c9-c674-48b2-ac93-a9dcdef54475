{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9436b4ebc37cac3d97710224e74d8985\\transformed\\play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4684,4790,4950,5077,5186,5329,5454,5574,5806,5962,6068,6230,6357,6502,6680,6746,6808", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "4785,4945,5072,5181,5324,5449,5569,5674,5957,6063,6225,6352,6497,6675,6741,6803,6881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\96abb1e1502d74c75d73c35e14f81e22\\transformed\\play-services-basement-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5679", "endColumns": "126", "endOffsets": "5801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b9676b1ec2d82f1cef53d671c6d0afde\\transformed\\material-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "94", "endOffsets": "145"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "15785", "endColumns": "94", "endOffsets": "15875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c0ee106e096d6fe0bd1735cc2a0a4c89\\transformed\\quickie-bundled-1.10.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,122", "endColumns": "66,63", "endOffsets": "117,181"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "19702,19769", "endColumns": "66,63", "endOffsets": "19764,19828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9d7219de08abf79892e9add9bb5b00cd\\transformed\\material-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3049,3127,3203,3281,3378,4185,4285,4434,7162,7221,7285,7371,7610,14829,14916,14980,15042,15104,15172,15237,15291,15409,15467,15528,15584,15659,15880,15966,16043,16134,16218,16298,16439,16517,16597,16719,16805,16883,16939,16990,17056,17124,17198,17269,17344,17416,17494,17564,17637,17741,17825,17902,17990,18079,18153,18226,18311,18360,18438,18504,18584,18667,18729,18793,18856,18925,19033,19136,19237,19336,19396,19622,20078,20158,20306", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "317,3122,3198,3276,3373,3453,4280,4429,4507,7216,7280,7366,7439,7665,14911,14975,15037,15099,15167,15232,15286,15404,15462,15523,15579,15654,15780,15961,16038,16129,16213,16293,16434,16512,16592,16714,16800,16878,16934,16985,17051,17119,17193,17264,17339,17411,17489,17559,17632,17736,17820,17897,17985,18074,18148,18221,18306,18355,18433,18499,18579,18662,18724,18788,18851,18920,19028,19131,19232,19331,19391,19446,19697,20153,20231,20378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\757e326fd7991b30514ac12e75a7701a\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,19998", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,20073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\bd9e012c025b7c80daa2bbfa6a34ffed\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,220", "endColumns": "74,89,86", "endOffsets": "125,215,302"}, "to": {"startLines": "33,224,225", "startColumns": "4,4,4", "startOffsets": "2974,21057,21147", "endColumns": "74,89,86", "endOffsets": "3044,21142,21229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f58a4462c0f661bc016516b04b05ffbf\\transformed\\core-1.16.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "39,40,41,42,43,44,45,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3458,3554,3656,3753,3851,3958,4067,20691", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3549,3651,3748,3846,3953,4062,4180,20787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b48892203169544dad7e4ce4c2aaf9a0\\transformed\\material3-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4576,4685,4792,4876,4961,5056,5157,5258,5338,5440,5524,5625,5724,5819,5919,6006,6111,6213,6318,6435,6515,6664,6796,6898,6997,7102", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,108,106,83,84,94,100,100,79,101,83,100,98,94,99,86,104,101,104,116,79,148,131,101,98,104,111", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4571,4680,4787,4871,4956,5051,5152,5253,5333,5435,5519,5620,5719,5814,5914,6001,6106,6208,6313,6430,6510,6659,6791,6893,6992,7097,7209"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7670,7786,7899,8006,8120,8220,8315,8427,8571,8693,8842,8926,9026,9115,9209,9323,9441,9546,9671,9791,9927,10100,10230,10347,10469,10588,10678,10776,10895,11031,11129,11247,11349,11475,11608,11713,11811,11891,11984,12077,12191,12300,12407,12491,12576,12671,12772,12873,12953,13055,13139,13240,13339,13434,13534,13621,13726,13828,13933,14050,14130,14279,14411,14513,14612,14717", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,108,106,83,84,94,100,100,79,101,83,100,98,94,99,86,104,101,104,116,79,148,131,101,98,104,111", "endOffsets": "7781,7894,8001,8115,8215,8310,8422,8566,8688,8837,8921,9021,9110,9204,9318,9436,9541,9666,9786,9922,10095,10225,10342,10464,10583,10673,10771,10890,11026,11124,11242,11344,11470,11603,11708,11806,11886,11979,12072,12186,12295,12402,12486,12571,12666,12767,12868,12948,13050,13134,13235,13334,13429,13529,13616,13721,13823,13928,14045,14125,14274,14406,14508,14607,14712,14824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\ec01753cc085eccabdf318240ae84988\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,273,353,448,547,629,706,795,884,966,1047,1131,1201,1292,1366,1438,1509,1587,1654", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "268,348,443,542,624,701,790,879,961,1042,1126,1196,1287,1361,1433,1504,1582,1649,1769"}, "to": {"startLines": "49,50,69,70,71,76,77,204,205,209,210,214,216,217,218,219,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4512,4604,6886,6981,7080,7444,7521,19451,19540,19833,19914,20236,20383,20474,20548,20620,20792,20870,20937", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "4599,4679,6976,7075,7157,7516,7605,19535,19617,19909,19993,20301,20469,20543,20615,20686,20865,20932,21052"}}]}]}