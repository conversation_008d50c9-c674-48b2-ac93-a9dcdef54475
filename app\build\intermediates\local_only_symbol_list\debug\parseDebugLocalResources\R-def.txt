R_DEF: Internal format may change without notice
local
color black
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color white
drawable small_icon
mipmap ic_launcher
mipmap ic_launcher_background
mipmap ic_launcher_foreground
mipmap ic_launcher_monochrome
string app_name
string assistant_page_add
string assistant_page_add_body
string assistant_page_add_header
string assistant_page_available_variables
string assistant_page_balanced
string assistant_page_body_key
string assistant_page_body_value
string assistant_page_cancel
string assistant_page_chaotic
string assistant_page_context_message_count
string assistant_page_context_message_desc
string assistant_page_context_message_size
string assistant_page_creative
string assistant_page_custom_bodies
string assistant_page_custom_headers
string assistant_page_default_assistant
string assistant_page_delete
string assistant_page_delete_body
string assistant_page_delete_header
string assistant_page_header_name
string assistant_page_header_value
string assistant_page_inject_message_time
string assistant_page_inject_message_time_desc
string assistant_page_invalid_json
string assistant_page_manage_memory
string assistant_page_manage_memory_title
string assistant_page_memory
string assistant_page_memory_count
string assistant_page_memory_desc
string assistant_page_name
string assistant_page_no_system_prompt
string assistant_page_save
string assistant_page_stream_output
string assistant_page_stream_output_desc
string assistant_page_strict
string assistant_page_system_prompt
string assistant_page_tab_basic
string assistant_page_tab_memory
string assistant_page_tab_prompt
string assistant_page_tab_request
string assistant_page_temperature
string assistant_page_temperature_value
string assistant_page_thinking_budget
string assistant_page_thinking_budget_default
string assistant_page_thinking_budget_desc
string assistant_page_thinking_budget_tokens
string assistant_page_thinking_budget_warning
string assistant_page_title
string assistant_page_top_p
string assistant_page_top_p_value
string assistant_page_top_p_warning
string back
string calculating
string cancel_edit
string chat_input_placeholder
string chat_page_cancel
string chat_page_clear_context
string chat_page_date_format_different_year
string chat_page_date_format_same_year
string chat_page_delete
string chat_page_edit_title
string chat_page_edit_title_warning
string chat_page_export_format
string chat_page_export_markdown
string chat_page_export_markdown_desc
string chat_page_export_share_via
string chat_page_export_success
string chat_page_history
string chat_page_new_chat
string chat_page_new_message
string chat_page_no_conversations
string chat_page_regenerate_title
string chat_page_restore_context
string chat_page_save
string chat_page_scroll_to_bottom
string chat_page_search_placeholder
string chat_page_today
string chat_page_yesterday
string citations_count
string code_block_copy
string code_block_preview
string com.google.firebase.crashlytics.mapping_file_id
string copy
string deep_thinking
string edit
string editing
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string menu_page_afternoon_greeting
string menu_page_ai_translator
string menu_page_evening_greeting
string menu_page_knowledge_base
string menu_page_llm_leaderboard
string menu_page_morning_greeting
string menu_page_night_greeting
string mermaid_export
string mermaid_export_failed
string mermaid_export_success
string model_list_chat
string model_list_embedding
string model_list_favorite
string model_list_no_providers
string model_list_select_model
string more_options
string notification_channel_chat_completed
string photo
string project_id
string regenerate
string send
string setting_display_page_auto_collapse_thinking_desc
string setting_display_page_auto_collapse_thinking_title
string setting_display_page_chat_list_model_icon_desc
string setting_display_page_chat_list_model_icon_title
string setting_display_page_show_token_usage_desc
string setting_display_page_show_token_usage_title
string setting_display_page_show_updates_desc
string setting_display_page_show_updates_title
string setting_display_page_title
string setting_model_page_chat_model
string setting_model_page_title
string setting_model_page_title_model
string setting_model_page_translate_model
string setting_page_about
string setting_page_about_desc
string setting_page_assistant
string setting_page_assistant_desc
string setting_page_chat_storage
string setting_page_chat_storage_desc
string setting_page_color_mode
string setting_page_color_mode_dark
string setting_page_color_mode_light
string setting_page_color_mode_system
string setting_page_config
string setting_page_config_api_desc
string setting_page_config_api_title
string setting_page_default_model
string setting_page_default_model_desc
string setting_page_display_setting
string setting_page_display_setting_desc
string setting_page_dynamic_color
string setting_page_dynamic_color_desc
string setting_page_general_settings
string setting_page_model_and_services
string setting_page_no_share_app
string setting_page_providers
string setting_page_providers_desc
string setting_page_search_service
string setting_page_search_service_desc
string setting_page_share
string setting_page_share_desc
string setting_page_share_text
string setting_page_theme_type_high_contrast
string setting_page_theme_type_medium_contrast
string setting_page_theme_type_standard
string settings
string stop
string take_picture
string theme_name_black
string theme_name_ocean
string theme_name_sakura
string theme_name_spring
string translator_page_cancel
string translator_page_input_placeholder
string translator_page_input_text
string translator_page_result
string translator_page_result_placeholder
string translator_page_target_language
string translator_page_title
string translator_page_translate
string tts
string use_web_search
style Theme.Rikkahub
xml _generated_res_locale_config
xml backup_rules
xml data_extraction_rules
xml file_paths
