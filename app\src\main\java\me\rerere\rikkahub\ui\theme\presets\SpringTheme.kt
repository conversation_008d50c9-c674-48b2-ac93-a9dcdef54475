package me.rerere.rikkahub.ui.theme.presets

import androidx.compose.material3.Text
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import me.rerere.rikkahub.R
import me.rerere.rikkahub.ui.theme.PresetTheme

val SpringThemePreset by lazy {
    PresetTheme(
        id = "spring",
        name = {
            Text(stringResource(id = R.string.theme_name_spring))
        },
        standardLight = lightScheme,
        standardDark = darkScheme,
        mediumContrastLight = mediumContrastLightColorScheme,
        mediumContrastDark = mediumContrastDarkColorScheme,
        highContrastLight = highContrastLightColorScheme,
        highContrastDark = highContrastDarkColorScheme,
    )
}

private val primaryLight = Color(0xFF4C662B)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFCDEDA3)
private val onPrimaryContainerLight = Color(0xFF354E16)
private val secondaryLight = Color(0xFF586249)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFDCE7C8)
private val onSecondaryContainerLight = Color(0xFF404A33)
private val tertiaryLight = Color(0xFF386663)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFBCECE7)
private val onTertiaryContainerLight = Color(0xFF1F4E4B)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF93000A)
private val backgroundLight = Color(0xFFF9FAEF)
private val onBackgroundLight = Color(0xFF1A1C16)
private val surfaceLight = Color(0xFFF9FAEF)
private val onSurfaceLight = Color(0xFF1A1C16)
private val surfaceVariantLight = Color(0xFFE1E4D5)
private val onSurfaceVariantLight = Color(0xFF44483D)
private val outlineLight = Color(0xFF75796C)
private val outlineVariantLight = Color(0xFFC5C8BA)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF2F312A)
private val inverseOnSurfaceLight = Color(0xFFF1F2E6)
private val inversePrimaryLight = Color(0xFFB1D18A)
private val surfaceDimLight = Color(0xFFDADBD0)
private val surfaceBrightLight = Color(0xFFF9FAEF)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFF3F4E9)
private val surfaceContainerLight = Color(0xFFEEEFE3)
private val surfaceContainerHighLight = Color(0xFFE8E9DE)
private val surfaceContainerHighestLight = Color(0xFFE2E3D8)

private val primaryLightMediumContrast = Color(0xFF253D05)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFF5A7539)
private val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryLightMediumContrast = Color(0xFF303924)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFF667157)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF083D3A)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFF477572)
private val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val errorLightMediumContrast = Color(0xFF740006)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFCF2C27)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFF9FAEF)
private val onBackgroundLightMediumContrast = Color(0xFF1A1C16)
private val surfaceLightMediumContrast = Color(0xFFF9FAEF)
private val onSurfaceLightMediumContrast = Color(0xFF0F120C)
private val surfaceVariantLightMediumContrast = Color(0xFFE1E4D5)
private val onSurfaceVariantLightMediumContrast = Color(0xFF34382D)
private val outlineLightMediumContrast = Color(0xFF505449)
private val outlineVariantLightMediumContrast = Color(0xFF6B6F62)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF2F312A)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFF1F2E6)
private val inversePrimaryLightMediumContrast = Color(0xFFB1D18A)
private val surfaceDimLightMediumContrast = Color(0xFFC6C7BD)
private val surfaceBrightLightMediumContrast = Color(0xFFF9FAEF)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFF3F4E9)
private val surfaceContainerLightMediumContrast = Color(0xFFE8E9DE)
private val surfaceContainerHighLightMediumContrast = Color(0xFFDCDED3)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFD1D3C8)

private val primaryLightHighContrast = Color(0xFF1C3200)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF375018)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF262F1A)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF434C35)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF003230)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF21504E)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF600004)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF98000A)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFF9FAEF)
private val onBackgroundLightHighContrast = Color(0xFF1A1C16)
private val surfaceLightHighContrast = Color(0xFFF9FAEF)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFE1E4D5)
private val onSurfaceVariantLightHighContrast = Color(0xFF000000)
private val outlineLightHighContrast = Color(0xFF2A2D24)
private val outlineVariantLightHighContrast = Color(0xFF474B40)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF2F312A)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFB1D18A)
private val surfaceDimLightHighContrast = Color(0xFFB8BAAF)
private val surfaceBrightLightHighContrast = Color(0xFFF9FAEF)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFF1F2E6)
private val surfaceContainerLightHighContrast = Color(0xFFE2E3D8)
private val surfaceContainerHighLightHighContrast = Color(0xFFD4D5CA)
private val surfaceContainerHighestLightHighContrast = Color(0xFFC6C7BD)

private val primaryDark = Color(0xFFB1D18A)
private val onPrimaryDark = Color(0xFF1F3701)
private val primaryContainerDark = Color(0xFF354E16)
private val onPrimaryContainerDark = Color(0xFFCDEDA3)
private val secondaryDark = Color(0xFFBFCBAD)
private val onSecondaryDark = Color(0xFF2A331E)
private val secondaryContainerDark = Color(0xFF404A33)
private val onSecondaryContainerDark = Color(0xFFDCE7C8)
private val tertiaryDark = Color(0xFFA0D0CB)
private val onTertiaryDark = Color(0xFF003735)
private val tertiaryContainerDark = Color(0xFF1F4E4B)
private val onTertiaryContainerDark = Color(0xFFBCECE7)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF12140E)
private val onBackgroundDark = Color(0xFFE2E3D8)
private val surfaceDark = Color(0xFF12140E)
private val onSurfaceDark = Color(0xFFE2E3D8)
private val surfaceVariantDark = Color(0xFF44483D)
private val onSurfaceVariantDark = Color(0xFFC5C8BA)
private val outlineDark = Color(0xFF8F9285)
private val outlineVariantDark = Color(0xFF44483D)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFE2E3D8)
private val inverseOnSurfaceDark = Color(0xFF2F312A)
private val inversePrimaryDark = Color(0xFF4C662B)
private val surfaceDimDark = Color(0xFF12140E)
private val surfaceBrightDark = Color(0xFF383A32)
private val surfaceContainerLowestDark = Color(0xFF0C0F09)
private val surfaceContainerLowDark = Color(0xFF1A1C16)
private val surfaceContainerDark = Color(0xFF1E201A)
private val surfaceContainerHighDark = Color(0xFF282B24)
private val surfaceContainerHighestDark = Color(0xFF33362E)

private val primaryDarkMediumContrast = Color(0xFFC7E79E)
private val onPrimaryDarkMediumContrast = Color(0xFF172B00)
private val primaryContainerDarkMediumContrast = Color(0xFF7D9A59)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
private val secondaryDarkMediumContrast = Color(0xFFD5E1C2)
private val onSecondaryDarkMediumContrast = Color(0xFF1F2814)
private val secondaryContainerDarkMediumContrast = Color(0xFF8A9579)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFB5E6E1)
private val onTertiaryDarkMediumContrast = Color(0xFF002B29)
private val tertiaryContainerDarkMediumContrast = Color(0xFF6B9995)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
private val errorDarkMediumContrast = Color(0xFFFFD2CC)
private val onErrorDarkMediumContrast = Color(0xFF540003)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF12140E)
private val onBackgroundDarkMediumContrast = Color(0xFFE2E3D8)
private val surfaceDarkMediumContrast = Color(0xFF12140E)
private val onSurfaceDarkMediumContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkMediumContrast = Color(0xFF44483D)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFDBDECF)
private val outlineDarkMediumContrast = Color(0xFFB0B3A6)
private val outlineVariantDarkMediumContrast = Color(0xFF8E9285)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFE2E3D8)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF282B24)
private val inversePrimaryDarkMediumContrast = Color(0xFF364F17)
private val surfaceDimDarkMediumContrast = Color(0xFF12140E)
private val surfaceBrightDarkMediumContrast = Color(0xFF43453D)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF060804)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF1C1E18)
private val surfaceContainerDarkMediumContrast = Color(0xFF262922)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF31342C)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF3C3F37)

private val primaryDarkHighContrast = Color(0xFFDAFBB0)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFFADCD86)
private val onPrimaryContainerDarkHighContrast = Color(0xFF050E00)
private val secondaryDarkHighContrast = Color(0xFFE9F4D5)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFBCC7A9)
private val onSecondaryContainerDarkHighContrast = Color(0xFF060D01)
private val tertiaryDarkHighContrast = Color(0xFFC9F9F5)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFF9CCCC7)
private val onTertiaryContainerDarkHighContrast = Color(0xFF000E0D)
private val errorDarkHighContrast = Color(0xFFFFECE9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFAEA4)
private val onErrorContainerDarkHighContrast = Color(0xFF220001)
private val backgroundDarkHighContrast = Color(0xFF12140E)
private val onBackgroundDarkHighContrast = Color(0xFFE2E3D8)
private val surfaceDarkHighContrast = Color(0xFF12140E)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF44483D)
private val onSurfaceVariantDarkHighContrast = Color(0xFFFFFFFF)
private val outlineDarkHighContrast = Color(0xFFEEF2E2)
private val outlineVariantDarkHighContrast = Color(0xFFC1C4B6)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFE2E3D8)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF364F17)
private val surfaceDimDarkHighContrast = Color(0xFF12140E)
private val surfaceBrightDarkHighContrast = Color(0xFF4F5149)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF000000)
private val surfaceContainerLowDarkHighContrast = Color(0xFF1E201A)
private val surfaceContainerDarkHighContrast = Color(0xFF2F312A)
private val surfaceContainerHighDarkHighContrast = Color(0xFF3A3C35)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF454840)

private val lightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

private val darkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)

private val mediumContrastLightColorScheme = lightColorScheme(
    primary = primaryLightMediumContrast,
    onPrimary = onPrimaryLightMediumContrast,
    primaryContainer = primaryContainerLightMediumContrast,
    onPrimaryContainer = onPrimaryContainerLightMediumContrast,
    secondary = secondaryLightMediumContrast,
    onSecondary = onSecondaryLightMediumContrast,
    secondaryContainer = secondaryContainerLightMediumContrast,
    onSecondaryContainer = onSecondaryContainerLightMediumContrast,
    tertiary = tertiaryLightMediumContrast,
    onTertiary = onTertiaryLightMediumContrast,
    tertiaryContainer = tertiaryContainerLightMediumContrast,
    onTertiaryContainer = onTertiaryContainerLightMediumContrast,
    error = errorLightMediumContrast,
    onError = onErrorLightMediumContrast,
    errorContainer = errorContainerLightMediumContrast,
    onErrorContainer = onErrorContainerLightMediumContrast,
    background = backgroundLightMediumContrast,
    onBackground = onBackgroundLightMediumContrast,
    surface = surfaceLightMediumContrast,
    onSurface = onSurfaceLightMediumContrast,
    surfaceVariant = surfaceVariantLightMediumContrast,
    onSurfaceVariant = onSurfaceVariantLightMediumContrast,
    outline = outlineLightMediumContrast,
    outlineVariant = outlineVariantLightMediumContrast,
    scrim = scrimLightMediumContrast,
    inverseSurface = inverseSurfaceLightMediumContrast,
    inverseOnSurface = inverseOnSurfaceLightMediumContrast,
    inversePrimary = inversePrimaryLightMediumContrast,
    surfaceDim = surfaceDimLightMediumContrast,
    surfaceBright = surfaceBrightLightMediumContrast,
    surfaceContainerLowest = surfaceContainerLowestLightMediumContrast,
    surfaceContainerLow = surfaceContainerLowLightMediumContrast,
    surfaceContainer = surfaceContainerLightMediumContrast,
    surfaceContainerHigh = surfaceContainerHighLightMediumContrast,
    surfaceContainerHighest = surfaceContainerHighestLightMediumContrast,
)

private val highContrastLightColorScheme = lightColorScheme(
    primary = primaryLightHighContrast,
    onPrimary = onPrimaryLightHighContrast,
    primaryContainer = primaryContainerLightHighContrast,
    onPrimaryContainer = onPrimaryContainerLightHighContrast,
    secondary = secondaryLightHighContrast,
    onSecondary = onSecondaryLightHighContrast,
    secondaryContainer = secondaryContainerLightHighContrast,
    onSecondaryContainer = onSecondaryContainerLightHighContrast,
    tertiary = tertiaryLightHighContrast,
    onTertiary = onTertiaryLightHighContrast,
    tertiaryContainer = tertiaryContainerLightHighContrast,
    onTertiaryContainer = onTertiaryContainerLightHighContrast,
    error = errorLightHighContrast,
    onError = onErrorLightHighContrast,
    errorContainer = errorContainerLightHighContrast,
    onErrorContainer = onErrorContainerLightHighContrast,
    background = backgroundLightHighContrast,
    onBackground = onBackgroundLightHighContrast,
    surface = surfaceLightHighContrast,
    onSurface = onSurfaceLightHighContrast,
    surfaceVariant = surfaceVariantLightHighContrast,
    onSurfaceVariant = onSurfaceVariantLightHighContrast,
    outline = outlineLightHighContrast,
    outlineVariant = outlineVariantLightHighContrast,
    scrim = scrimLightHighContrast,
    inverseSurface = inverseSurfaceLightHighContrast,
    inverseOnSurface = inverseOnSurfaceLightHighContrast,
    inversePrimary = inversePrimaryLightHighContrast,
    surfaceDim = surfaceDimLightHighContrast,
    surfaceBright = surfaceBrightLightHighContrast,
    surfaceContainerLowest = surfaceContainerLowestLightHighContrast,
    surfaceContainerLow = surfaceContainerLowLightHighContrast,
    surfaceContainer = surfaceContainerLightHighContrast,
    surfaceContainerHigh = surfaceContainerHighLightHighContrast,
    surfaceContainerHighest = surfaceContainerHighestLightHighContrast,
)

private val mediumContrastDarkColorScheme = darkColorScheme(
    primary = primaryDarkMediumContrast,
    onPrimary = onPrimaryDarkMediumContrast,
    primaryContainer = primaryContainerDarkMediumContrast,
    onPrimaryContainer = onPrimaryContainerDarkMediumContrast,
    secondary = secondaryDarkMediumContrast,
    onSecondary = onSecondaryDarkMediumContrast,
    secondaryContainer = secondaryContainerDarkMediumContrast,
    onSecondaryContainer = onSecondaryContainerDarkMediumContrast,
    tertiary = tertiaryDarkMediumContrast,
    onTertiary = onTertiaryDarkMediumContrast,
    tertiaryContainer = tertiaryContainerDarkMediumContrast,
    onTertiaryContainer = onTertiaryContainerDarkMediumContrast,
    error = errorDarkMediumContrast,
    onError = onErrorDarkMediumContrast,
    errorContainer = errorContainerDarkMediumContrast,
    onErrorContainer = onErrorContainerDarkMediumContrast,
    background = backgroundDarkMediumContrast,
    onBackground = onBackgroundDarkMediumContrast,
    surface = surfaceDarkMediumContrast,
    onSurface = onSurfaceDarkMediumContrast,
    surfaceVariant = surfaceVariantDarkMediumContrast,
    onSurfaceVariant = onSurfaceVariantDarkMediumContrast,
    outline = outlineDarkMediumContrast,
    outlineVariant = outlineVariantDarkMediumContrast,
    scrim = scrimDarkMediumContrast,
    inverseSurface = inverseSurfaceDarkMediumContrast,
    inverseOnSurface = inverseOnSurfaceDarkMediumContrast,
    inversePrimary = inversePrimaryDarkMediumContrast,
    surfaceDim = surfaceDimDarkMediumContrast,
    surfaceBright = surfaceBrightDarkMediumContrast,
    surfaceContainerLowest = surfaceContainerLowestDarkMediumContrast,
    surfaceContainerLow = surfaceContainerLowDarkMediumContrast,
    surfaceContainer = surfaceContainerDarkMediumContrast,
    surfaceContainerHigh = surfaceContainerHighDarkMediumContrast,
    surfaceContainerHighest = surfaceContainerHighestDarkMediumContrast,
)

private val highContrastDarkColorScheme = darkColorScheme(
    primary = primaryDarkHighContrast,
    onPrimary = onPrimaryDarkHighContrast,
    primaryContainer = primaryContainerDarkHighContrast,
    onPrimaryContainer = onPrimaryContainerDarkHighContrast,
    secondary = secondaryDarkHighContrast,
    onSecondary = onSecondaryDarkHighContrast,
    secondaryContainer = secondaryContainerDarkHighContrast,
    onSecondaryContainer = onSecondaryContainerDarkHighContrast,
    tertiary = tertiaryDarkHighContrast,
    onTertiary = onTertiaryDarkHighContrast,
    tertiaryContainer = tertiaryContainerDarkHighContrast,
    onTertiaryContainer = onTertiaryContainerDarkHighContrast,
    error = errorDarkHighContrast,
    onError = onErrorDarkHighContrast,
    errorContainer = errorContainerDarkHighContrast,
    onErrorContainer = onErrorContainerDarkHighContrast,
    background = backgroundDarkHighContrast,
    onBackground = onBackgroundDarkHighContrast,
    surface = surfaceDarkHighContrast,
    onSurface = onSurfaceDarkHighContrast,
    surfaceVariant = surfaceVariantDarkHighContrast,
    onSurfaceVariant = onSurfaceVariantDarkHighContrast,
    outline = outlineDarkHighContrast,
    outlineVariant = outlineVariantDarkHighContrast,
    scrim = scrimDarkHighContrast,
    inverseSurface = inverseSurfaceDarkHighContrast,
    inverseOnSurface = inverseOnSurfaceDarkHighContrast,
    inversePrimary = inversePrimaryDarkHighContrast,
    surfaceDim = surfaceDimDarkHighContrast,
    surfaceBright = surfaceBrightDarkHighContrast,
    surfaceContainerLowest = surfaceContainerLowestDarkHighContrast,
    surfaceContainerLow = surfaceContainerLowDarkHighContrast,
    surfaceContainer = surfaceContainerDarkHighContrast,
    surfaceContainerHigh = surfaceContainerHighDarkHighContrast,
    surfaceContainerHighest = surfaceContainerHighestDarkHighContrast,
)
