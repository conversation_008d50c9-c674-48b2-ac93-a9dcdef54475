package me.rerere.rikkahub.ui.theme

import androidx.compose.ui.graphics.Color
import me.rerere.highlight.HighlightTextColorPalette

// Atom One Dark Theme Colors
val AtomOneDarkPalette = HighlightTextColorPalette(
    keyword = Color(0xFFc678dd),    // 紫色
    string = Color(0xFF98c379),     // 绿色
    number = Color(0xFFd19a66),     // 橙色
    comment = Color(0xFF5c6370),    // 灰色
    function = Color(0xFF61afef),   // 蓝色
    operator = Color(0xFF56b6c2),   // 青色
    punctuation = Color(0xFFabb2bf), // 浅灰色
    className = Color(0xFFe5c07b),  // 黄色
    property = Color(0xFFe06c75),   // 红色
    boolean = Color(0xFFd19a66),    // 橙色
    variable = Color(0xFFe06c75),   // 红色
    tag = Color(0xFFe06c75),        // 红色
    attrName = Color(0xFFd19a66),   // 橙色
    attrValue = Color(0xFF98c379),  // 绿色
    fallback = Color(0xFFabb2bf)    // 浅灰色
)

// Atom One Light Theme Colors
val AtomOneLightPalette = HighlightTextColorPalette(
    keyword = Color(0xFFa626a4),    // 紫色
    string = Color(0xFF50a14f),     // 绿色
    number = Color(0xFFc18401),     // 橙色
    comment = Color(0xFF9ca0a4),    // 灰色
    function = Color(0xFF4078f2),   // 蓝色
    operator = Color(0xFF0184bc),   // 青色
    punctuation = Color(0xFF383a42), // 深灰色
    className = Color(0xFFc18401),  // 黄色
    property = Color(0xFFe45649),   // 红色
    boolean = Color(0xFFc18401),    // 橙色
    variable = Color(0xFFe45649),   // 红色
    tag = Color(0xFFe45649),        // 红色
    attrName = Color(0xFFc18401),   // 橙色
    attrValue = Color(0xFF50a14f),  // 绿色
    fallback = Color(0xFF383a42)    // 深灰色
)