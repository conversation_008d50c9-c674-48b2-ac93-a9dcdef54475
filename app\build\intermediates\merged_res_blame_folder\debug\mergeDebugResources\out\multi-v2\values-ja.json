{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\aaf9750c511786fa108e7a3c496402ad\\transformed\\play-services-basement-18.5.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "10903", "endColumns": "121", "endOffsets": "11020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\835032a51b5d40afa0ab30aa91122c7e\\transformed\\quickie-bundled-1.10.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endColumns": "58,62", "endOffsets": "109,172"}, "to": {"startLines": "311,312", "startColumns": "4,4", "startOffsets": "25001,25060", "endColumns": "58,62", "endOffsets": "25055,25118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8bf40832b048d1f4d2354372c996192d\\transformed\\material3-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,273,382,488,581,671,778,892,1000,1124,1206,1303,1388,1478,1585,1698,1800,1924,2046,2160,2287,2397,2498,2602,2710,2796,2891,2999,3111,3202,3299,3396,3517,3643,3742,3834,3909,4002,4094,4195,4294,4391,4474,4557,4644,4741,4832,4912,5005,5087,5185,5280,5373,5470,5553,5649,5744,5842,5953,6033,6146,6254,6351,6445,6547", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,100,98,96,82,82,86,96,90,79,92,81,97,94,92,96,82,95,94,97,110,79,112,107,96,93,101,105", "endOffsets": "161,268,377,483,576,666,773,887,995,1119,1201,1298,1383,1473,1580,1693,1795,1919,2041,2155,2282,2392,2493,2597,2705,2791,2886,2994,3106,3197,3294,3391,3512,3638,3737,3829,3904,3997,4089,4190,4289,4386,4469,4552,4639,4736,4827,4907,5000,5082,5180,5275,5368,5465,5548,5644,5739,5837,5948,6028,6141,6249,6346,6440,6542,6648"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12850,12961,13068,13177,13283,13376,13466,13573,13687,13795,13919,14001,14098,14183,14273,14380,14493,14595,14719,14841,14955,15082,15192,15293,15397,15505,15591,15686,15794,15906,15997,16094,16191,16312,16438,16537,16629,16704,16797,16889,16990,17089,17186,17269,17352,17439,17536,17627,17707,17800,17882,17980,18075,18168,18265,18348,18444,18539,18637,18748,18828,18941,19049,19146,19240,19342", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,100,98,96,82,82,86,96,90,79,92,81,97,94,92,96,82,95,94,97,110,79,112,107,96,93,101,105", "endOffsets": "12956,13063,13172,13278,13371,13461,13568,13682,13790,13914,13996,14093,14178,14268,14375,14488,14590,14714,14836,14950,15077,15187,15288,15392,15500,15586,15681,15789,15901,15992,16089,16186,16307,16433,16532,16624,16699,16792,16884,16985,17084,17181,17264,17347,17434,17531,17622,17702,17795,17877,17975,18070,18163,18260,18343,18439,18534,18632,18743,18823,18936,19044,19141,19235,19337,19443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5aea9c81c7615102d7b5640b77daf851\\transformed\\uCrop-n-Edit-4.1.0-non-native\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,97,153,208,257,301", "endColumns": "41,55,54,48,43,42", "endOffsets": "92,148,203,252,296,339"}, "to": {"startLines": "393,394,395,396,397,398", "startColumns": "4,4,4,4,4,4", "startOffsets": "30649,30691,30747,30802,30851,30895", "endColumns": "41,55,54,48,43,42", "endOffsets": "30686,30742,30797,30846,30890,30933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4e69add8b1376897dea52ac0f7720f60\\transformed\\ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,263,341,430,527,610,688,766,851,926,1000,1076,1145,1228,1301,1373,1443,1519,1584", "endColumns": "85,77,88,96,82,77,77,84,74,73,75,68,82,72,71,69,75,64,116", "endOffsets": "258,336,425,522,605,683,761,846,921,995,1071,1140,1223,1296,1368,1438,1514,1579,1696"}, "to": {"startLines": "129,130,153,154,155,162,163,306,307,313,314,319,368,369,370,371,374,375,377", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9714,9800,12027,12116,12213,12636,12714,24660,24745,25123,25197,25546,28978,29061,29134,29206,29413,29489,29601", "endColumns": "85,77,88,96,82,77,77,84,74,73,75,68,82,72,71,69,75,64,116", "endOffsets": "9795,9873,12111,12208,12291,12709,12787,24740,24815,25192,25268,25610,29056,29129,29201,29271,29484,29549,29713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\303c2166503144bea901100ae0f040af\\transformed\\foundation-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,211", "endColumns": "70,84,81", "endOffsets": "121,206,288"}, "to": {"startLines": "85,382,383", "startColumns": "4,4,4", "startOffsets": "6671,29935,30020", "endColumns": "70,84,81", "endOffsets": "6737,30015,30097"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\src\\main\\res\\values-ja\\strings.xml", "from": {"startLines": "2,94,139,133,97,100,135,136,111,102,107,106,105,101,134,129,115,114,138,132,130,131,120,121,137,110,113,108,117,109,95,118,112,202,203,99,96,198,200,199,201,98,116,122,125,123,124,126,93,103,119,104,18,66,5,3,34,38,27,26,29,32,36,42,43,44,46,45,30,35,23,22,28,39,33,31,37,24,25,17,172,173,12,16,14,4,157,160,158,161,162,156,159,176,178,177,168,169,167,166,165,8,142,10,13,7,187,186,183,182,185,184,189,188,181,78,77,79,80,62,63,60,61,64,65,192,195,194,193,73,72,71,54,55,48,49,50,51,52,53,70,56,57,58,59,67,68,69,147,146,145,19,6,11,150,152,151,153,90,85,84,86,87,88,83,89,15,9", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,5088,8401,8014,5264,5453,8143,8202,6361,5568,6003,5857,5777,5511,8077,7755,6594,6540,8338,7948,7823,7885,6940,7015,8269,6290,6470,6088,6737,6142,5139,6801,6418,11658,11726,5399,5196,11414,11534,11473,11592,5340,6668,7174,7445,7242,7370,7519,5029,5628,6874,5684,789,3585,215,104,1611,1865,1190,1118,1337,1506,1719,2022,2084,2148,2303,2233,1386,1663,958,893,1272,1929,1564,1440,1801,1018,1066,734,9942,9995,530,685,612,174,9160,9391,9236,9449,9515,9082,9310,10081,10198,10132,9810,9860,9755,9670,9610,339,8490,444,568,302,10824,10734,10454,10361,10645,10561,11004,10925,10300,4239,4179,4306,4376,3324,3380,3186,3245,3443,3505,11121,11314,11251,11181,4090,4015,3939,2780,2845,2366,2429,2500,2566,2646,2710,3870,2922,2981,3050,3114,3633,3684,3749,8732,8655,8590,826,265,482,8835,8946,8892,9001,4940,4595,4532,4679,4736,4815,4478,4882,649,391", "endColumns": "45,49,58,61,74,56,57,65,55,58,83,144,78,55,64,66,72,52,61,64,60,61,73,157,67,69,68,52,62,146,55,71,50,66,83,52,66,57,56,59,64,57,67,66,72,126,73,192,57,54,64,91,35,46,48,68,50,62,80,70,47,56,80,60,62,83,59,68,52,54,58,63,63,63,45,64,62,46,50,53,51,52,36,47,35,39,74,56,72,64,62,76,79,49,63,64,48,53,53,83,58,50,70,36,42,35,99,88,105,91,87,82,88,77,59,65,58,68,68,54,61,57,77,60,78,58,60,61,68,50,73,74,63,75,61,69,64,78,62,68,67,57,67,62,70,49,63,119,73,75,63,39,35,46,55,53,52,53,56,82,61,55,77,65,52,56,34,51", "endOffsets": "98,5133,8455,8071,5334,5505,8196,8263,6412,5622,6082,5997,5851,5562,8137,7817,6662,6588,8395,8008,7879,7942,7009,7168,8332,6355,6534,6136,6795,6284,5190,6868,6464,11720,11805,5447,5258,11467,11586,11528,11652,5393,6731,7236,7513,7364,7439,7707,5082,5678,6934,5771,820,3627,259,168,1657,1923,1266,1184,1380,1558,1795,2078,2142,2227,2358,2297,1434,1713,1012,952,1331,1988,1605,1500,1859,1060,1112,783,9989,10043,562,728,643,209,9230,9443,9304,9509,9573,9154,9385,10126,10257,10192,9854,9909,9804,9749,9664,385,8556,476,606,333,10919,10818,10555,10448,10728,10639,11088,10998,10355,4300,4233,4370,4440,3374,3437,3239,3318,3499,3579,11175,11370,11308,11245,4136,4084,4009,2839,2916,2423,2494,2560,2640,2704,2774,3933,2975,3044,3108,3180,3678,3743,3864,8801,8726,8649,861,296,524,8886,8995,8940,9050,4992,4673,4589,4730,4809,4876,4526,4934,679,438"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,86,92,100,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,131,132,151,152,156,157,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,308,310,315,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,373,376,378,379,380,381,384,385,386,387,388,389,390,391,392,399", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2889,2935,2985,3044,3106,3181,3238,3296,3362,3418,3477,3561,3706,3785,3841,3906,3973,4046,4099,4161,4226,4287,4349,4423,4581,4649,4719,4788,4841,4904,5051,5107,5179,5230,5297,5381,5434,5501,5559,5616,5676,5741,5799,5867,5934,6007,6134,6208,6401,6459,6514,6579,6742,7141,7857,8095,8164,8215,8278,8359,8430,8478,8535,8616,8677,8740,8824,8884,8953,9006,9061,9120,9184,9248,9312,9358,9423,9486,9533,9584,9878,9930,11942,11979,12296,12332,20392,20467,20524,20597,20662,20725,20802,20882,20932,20996,21061,21110,21164,21218,21302,21361,24820,24964,25273,25615,25651,25751,25840,25946,26038,26126,26209,26298,26376,26436,26502,26561,26630,26699,26754,26816,26874,26952,27013,27092,27151,27212,27274,27343,27394,27468,27543,27607,27683,27745,27815,27880,27959,28022,28091,28159,28217,28285,28348,28419,28469,28533,28653,28727,28803,28867,29377,29554,29718,29774,29828,29881,30102,30159,30242,30304,30360,30438,30504,30557,30614,30938", "endColumns": "45,49,58,61,74,56,57,65,55,58,83,144,78,55,64,66,72,52,61,64,60,61,73,157,67,69,68,52,62,146,55,71,50,66,83,52,66,57,56,59,64,57,67,66,72,126,73,192,57,54,64,91,35,46,48,68,50,62,80,70,47,56,80,60,62,83,59,68,52,54,58,63,63,63,45,64,62,46,50,53,51,52,36,47,35,39,74,56,72,64,62,76,79,49,63,64,48,53,53,83,58,50,70,36,42,35,99,88,105,91,87,82,88,77,59,65,58,68,68,54,61,57,77,60,78,58,60,61,68,50,73,74,63,75,61,69,64,78,62,68,67,57,67,62,70,49,63,119,73,75,63,39,35,46,55,53,52,53,56,82,61,55,77,65,52,56,34,51", "endOffsets": "2930,2980,3039,3101,3176,3233,3291,3357,3413,3472,3556,3701,3780,3836,3901,3968,4041,4094,4156,4221,4282,4344,4418,4576,4644,4714,4783,4836,4899,5046,5102,5174,5225,5292,5376,5429,5496,5554,5611,5671,5736,5794,5862,5929,6002,6129,6203,6396,6454,6509,6574,6666,6773,7183,7901,8159,8210,8273,8354,8425,8473,8530,8611,8672,8735,8819,8879,8948,9001,9056,9115,9179,9243,9307,9353,9418,9481,9528,9579,9633,9925,9978,11974,12022,12327,12367,20462,20519,20592,20657,20720,20797,20877,20927,20991,21056,21105,21159,21213,21297,21356,21407,24886,24996,25311,25646,25746,25835,25941,26033,26121,26204,26293,26371,26431,26497,26556,26625,26694,26749,26811,26869,26947,27008,27087,27146,27207,27269,27338,27389,27463,27538,27602,27678,27740,27810,27875,27954,28017,28086,28154,28212,28280,28343,28414,28464,28528,28648,28722,28798,28862,28902,29408,29596,29769,29823,29876,29930,30154,30237,30299,30355,30433,30499,30552,30609,30644,30985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2f477ab85e7d6008688a1a0c4b191573\\transformed\\material-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "244", "startColumns": "4", "startOffsets": "20305", "endColumns": "86", "endOffsets": "20387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\be5cc04b6a4fee62d71666ba2f889b70\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,316", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,25316", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,25390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d2e6b12382a4dadf34d77e521366ccff\\transformed\\play-services-base-18.5.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "133,134,135,136,137,138,139,140,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9983,10087,10221,10341,10447,10579,10699,10804,11025,11159,11260,11393,11512,11632,11752,11812,11871", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "10082,10216,10336,10442,10574,10694,10799,10898,11154,11255,11388,11507,11627,11747,11807,11866,11937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d642686678dc9e4dc645c444fc2595b8\\transformed\\core-1.16.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "93,94,95,96,97,98,99,372", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "7188,7280,7380,7474,7570,7663,7756,29276", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "7275,7375,7469,7565,7658,7751,7852,29372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b26bc0a945bb328544f80db00c932160\\transformed\\material-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,934,997,1081,1145,1203,1284,1345,1409,1464,1523,1580,1634,1727,1783,1840,1894,1960,2060,2136,2207,2286,2359,2440,2562,2624,2686,2787,2866,2941,2994,3045,3111,3181,3251,3322,3392,3456,3527,3595,3658,3749,3828,3891,3971,4053,4125,4196,4268,4316,4388,4452,4527,4604,4666,4730,4793,4860,4946,5032,5113,5196,5253,5308,5381,5459,5532", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "248,315,379,448,529,611,696,800,876,929,992,1076,1140,1198,1279,1340,1404,1459,1518,1575,1629,1722,1778,1835,1889,1955,2055,2131,2202,2281,2354,2435,2557,2619,2681,2782,2861,2936,2989,3040,3106,3176,3246,3317,3387,3451,3522,3590,3653,3744,3823,3886,3966,4048,4120,4191,4263,4311,4383,4447,4522,4599,4661,4725,4788,4855,4941,5027,5108,5191,5248,5303,5376,5454,5527,5598"}, "to": {"startLines": "2,87,88,89,90,91,101,102,128,158,159,160,161,164,231,232,233,234,235,236,237,238,239,240,241,242,243,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,309,317,318,367", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,6778,6845,6909,6978,7059,7906,7991,9638,12372,12425,12488,12572,12792,19448,19529,19590,19654,19709,19768,19825,19879,19972,20028,20085,20139,20205,21412,21488,21559,21638,21711,21792,21914,21976,22038,22139,22218,22293,22346,22397,22463,22533,22603,22674,22744,22808,22879,22947,23010,23101,23180,23243,23323,23405,23477,23548,23620,23668,23740,23804,23879,23956,24018,24082,24145,24212,24298,24384,24465,24548,24605,24891,25395,25473,28907", "endLines": "5,87,88,89,90,91,101,102,128,158,159,160,161,164,231,232,233,234,235,236,237,238,239,240,241,242,243,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,309,317,318,367", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "298,6840,6904,6973,7054,7136,7986,8090,9709,12420,12483,12567,12631,12845,19524,19585,19649,19704,19763,19820,19874,19967,20023,20080,20134,20200,20300,21483,21554,21633,21706,21787,21909,21971,22033,22134,22213,22288,22341,22392,22458,22528,22598,22669,22739,22803,22874,22942,23005,23096,23175,23238,23318,23400,23472,23543,23615,23663,23735,23799,23874,23951,24013,24077,24140,24207,24293,24379,24460,24543,24600,24655,24959,25468,25541,28973"}}]}]}