package me.rerere.rikkahub.`data`.db.dao

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.coroutines.createFlow
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import javax.`annotation`.processing.Generated
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.Flow
import me.rerere.rikkahub.`data`.db.entity.MemoryEntity

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class MemoryDAO_Impl(
  __db: RoomDatabase,
) : MemoryDAO {
  private val __db: RoomDatabase

  private val __insertAdapterOfMemoryEntity: EntityInsertAdapter<MemoryEntity>

  private val __updateAdapterOfMemoryEntity: EntityDeleteOrUpdateAdapter<MemoryEntity>
  init {
    this.__db = __db
    this.__insertAdapterOfMemoryEntity = object : EntityInsertAdapter<MemoryEntity>() {
      protected override fun createQuery(): String =
          "INSERT OR ABORT INTO `MemoryEntity` (`id`,`assistant_id`,`content`) VALUES (nullif(?, 0),?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: MemoryEntity) {
        statement.bindLong(1, entity.id.toLong())
        statement.bindText(2, entity.assistantId)
        statement.bindText(3, entity.content)
      }
    }
    this.__updateAdapterOfMemoryEntity = object : EntityDeleteOrUpdateAdapter<MemoryEntity>() {
      protected override fun createQuery(): String =
          "UPDATE OR ABORT `MemoryEntity` SET `id` = ?,`assistant_id` = ?,`content` = ? WHERE `id` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: MemoryEntity) {
        statement.bindLong(1, entity.id.toLong())
        statement.bindText(2, entity.assistantId)
        statement.bindText(3, entity.content)
        statement.bindLong(4, entity.id.toLong())
      }
    }
  }

  public override suspend fun insertMemory(memory: MemoryEntity): Long = performSuspending(__db,
      false, true) { _connection ->
    val _result: Long = __insertAdapterOfMemoryEntity.insertAndReturnId(_connection, memory)
    _result
  }

  public override suspend fun updateMemory(memory: MemoryEntity): Unit = performSuspending(__db,
      false, true) { _connection ->
    __updateAdapterOfMemoryEntity.handle(_connection, memory)
  }

  public override fun getMemoriesOfAssistantFlow(assistantId: String): Flow<List<MemoryEntity>> {
    val _sql: String = "SELECT * FROM memoryentity WHERE assistant_id = ?"
    return createFlow(__db, false, arrayOf("memoryentity")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, assistantId)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfAssistantId: Int = getColumnIndexOrThrow(_stmt, "assistant_id")
        val _columnIndexOfContent: Int = getColumnIndexOrThrow(_stmt, "content")
        val _result: MutableList<MemoryEntity> = mutableListOf()
        while (_stmt.step()) {
          val _item: MemoryEntity
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpAssistantId: String
          _tmpAssistantId = _stmt.getText(_columnIndexOfAssistantId)
          val _tmpContent: String
          _tmpContent = _stmt.getText(_columnIndexOfContent)
          _item = MemoryEntity(_tmpId,_tmpAssistantId,_tmpContent)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getMemoriesOfAssistant(assistantId: String): List<MemoryEntity> {
    val _sql: String = "SELECT * FROM memoryentity WHERE assistant_id = ?"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, assistantId)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfAssistantId: Int = getColumnIndexOrThrow(_stmt, "assistant_id")
        val _columnIndexOfContent: Int = getColumnIndexOrThrow(_stmt, "content")
        val _result: MutableList<MemoryEntity> = mutableListOf()
        while (_stmt.step()) {
          val _item: MemoryEntity
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpAssistantId: String
          _tmpAssistantId = _stmt.getText(_columnIndexOfAssistantId)
          val _tmpContent: String
          _tmpContent = _stmt.getText(_columnIndexOfContent)
          _item = MemoryEntity(_tmpId,_tmpAssistantId,_tmpContent)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getMemoryById(id: Int): MemoryEntity? {
    val _sql: String = "SELECT * FROM memoryentity WHERE id = ?"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, id.toLong())
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfAssistantId: Int = getColumnIndexOrThrow(_stmt, "assistant_id")
        val _columnIndexOfContent: Int = getColumnIndexOrThrow(_stmt, "content")
        val _result: MemoryEntity?
        if (_stmt.step()) {
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpAssistantId: String
          _tmpAssistantId = _stmt.getText(_columnIndexOfAssistantId)
          val _tmpContent: String
          _tmpContent = _stmt.getText(_columnIndexOfContent)
          _result = MemoryEntity(_tmpId,_tmpAssistantId,_tmpContent)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteMemory(id: Int) {
    val _sql: String = "DELETE FROM memoryentity WHERE id = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, id.toLong())
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteMemoriesOfAssistant(assistantId: String) {
    val _sql: String = "DELETE FROM memoryentity WHERE assistant_id = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, assistantId)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
