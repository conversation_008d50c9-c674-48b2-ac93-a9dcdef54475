package me.rerere.rikkahub.`data`.db

import androidx.room.migration.Migration
import androidx.sqlite.SQLiteConnection
import javax.`annotation`.processing.Generated
import kotlin.Suppress

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
internal class AppDatabase_AutoMigration_2_3_Impl : Migration {
  public constructor() : super(2, 3)

  public override fun migrate(connection: SQLiteConnection) {
  }
}
