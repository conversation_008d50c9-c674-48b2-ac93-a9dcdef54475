{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8bf40832b048d1f4d2354372c996192d\\transformed\\material3-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,299,422,542,642,740,855,998,1116,1268,1353,1455,1552,1654,1772,1895,2002,2138,2271,2410,2592,2723,2843,2965,3092,3190,3286,3407,3540,3641,3746,3861,3996,4137,4248,4353,4430,4526,4621,4742,4864,4984,5071,5160,5249,5360,5461,5541,5647,5731,5832,5938,6038,6137,6225,6340,6441,6545,6668,6748,6895,7028,7135,7234,7340", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,120,121,119,86,88,88,110,100,79,105,83,100,105,99,98,87,114,100,103,122,79,146,132,106,98,105,112", "endOffsets": "172,294,417,537,637,735,850,993,1111,1263,1348,1450,1547,1649,1767,1890,1997,2133,2266,2405,2587,2718,2838,2960,3087,3185,3281,3402,3535,3636,3741,3856,3991,4132,4243,4348,4425,4521,4616,4737,4859,4979,5066,5155,5244,5355,5456,5536,5642,5726,5827,5933,6033,6132,6220,6335,6436,6540,6663,6743,6890,7023,7130,7229,7335,7448"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7946,8068,8190,8313,8433,8533,8631,8746,8889,9007,9159,9244,9346,9443,9545,9663,9786,9893,10029,10162,10301,10483,10614,10734,10856,10983,11081,11177,11298,11431,11532,11637,11752,11887,12028,12139,12244,12321,12417,12512,12633,12755,12875,12962,13051,13140,13251,13352,13432,13538,13622,13723,13829,13929,14028,14116,14231,14332,14436,14559,14639,14786,14919,15026,15125,15231", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,120,121,119,86,88,88,110,100,79,105,83,100,105,99,98,87,114,100,103,122,79,146,132,106,98,105,112", "endOffsets": "8063,8185,8308,8428,8528,8626,8741,8884,9002,9154,9239,9341,9438,9540,9658,9781,9888,10024,10157,10296,10478,10609,10729,10851,10978,11076,11172,11293,11426,11527,11632,11747,11882,12023,12134,12239,12316,12412,12507,12628,12750,12870,12957,13046,13135,13246,13347,13427,13533,13617,13718,13824,13924,14023,14111,14226,14327,14431,14554,14634,14781,14914,15021,15120,15226,15339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4e69add8b1376897dea52ac0f7720f60\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,285,367,465,568,657,736,832,924,1011,1098,1188,1265,1350,1426,1506,1582,1660,1730", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,84,75,79,75,77,69,122", "endOffsets": "280,362,460,563,652,731,827,919,1006,1093,1183,1260,1345,1421,1501,1577,1655,1725,1848"}, "to": {"startLines": "49,50,69,70,71,76,77,204,205,209,210,214,216,217,218,219,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4616,4715,7126,7224,7327,7708,7787,20183,20275,20584,20671,21003,21157,21242,21318,21398,21575,21653,21723", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,84,75,79,75,77,69,122", "endOffsets": "4710,4792,7219,7322,7411,7782,7878,20270,20357,20666,20756,21075,21237,21313,21393,21469,21648,21718,21841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\303c2166503144bea901100ae0f040af\\transformed\\foundation-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,235", "endColumns": "79,99,101", "endOffsets": "130,230,332"}, "to": {"startLines": "33,224,225", "startColumns": "4,4,4", "startOffsets": "3044,21846,21946", "endColumns": "79,99,101", "endOffsets": "3119,21941,22043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d642686678dc9e4dc645c444fc2595b8\\transformed\\core-1.16.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "39,40,41,42,43,44,45,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3573,3672,3774,3874,3972,4079,4185,21474", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3667,3769,3869,3967,4074,4180,4300,21570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d2e6b12382a4dadf34d77e521366ccff\\transformed\\play-services-base-18.5.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4797,4905,5071,5203,5311,5472,5603,5726,5978,6149,6258,6428,6561,6738,6916,6986,7048", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "4900,5066,5198,5306,5467,5598,5721,5827,6144,6253,6423,6556,6733,6911,6981,7043,7121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2f477ab85e7d6008688a1a0c4b191573\\transformed\\material-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "16338", "endColumns": "88", "endOffsets": "16422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b26bc0a945bb328544f80db00c932160\\transformed\\material-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1100,1165,1260,1330,1393,1486,1550,1622,1685,1759,1823,1879,1997,2055,2117,2173,2253,2387,2476,2552,2650,2731,2812,2953,3034,3114,3265,3355,3432,3488,3544,3610,3689,3771,3842,3931,4004,4081,4151,4228,4334,4423,4497,4591,4693,4765,4846,4950,5003,5088,5155,5248,5337,5399,5463,5526,5594,5705,5816,5918,6023,6083,6143,6226,6309,6385", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "273,355,435,521,626,722,824,952,1033,1095,1160,1255,1325,1388,1481,1545,1617,1680,1754,1818,1874,1992,2050,2112,2168,2248,2382,2471,2547,2645,2726,2807,2948,3029,3109,3260,3350,3427,3483,3539,3605,3684,3766,3837,3926,3999,4076,4146,4223,4329,4418,4492,4586,4688,4760,4841,4945,4998,5083,5150,5243,5332,5394,5458,5521,5589,5700,5811,5913,6018,6078,6138,6221,6304,6380,6457"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3124,3206,3286,3372,3477,4305,4407,4535,7416,7478,7543,7638,7883,15344,15437,15501,15573,15636,15710,15774,15830,15948,16006,16068,16124,16204,16427,16516,16592,16690,16771,16852,16993,17074,17154,17305,17395,17472,17528,17584,17650,17729,17811,17882,17971,18044,18121,18191,18268,18374,18463,18537,18631,18733,18805,18886,18990,19043,19128,19195,19288,19377,19439,19503,19566,19634,19745,19856,19958,20063,20123,20362,20844,20927,21080", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "323,3201,3281,3367,3472,3568,4402,4530,4611,7473,7538,7633,7703,7941,15432,15496,15568,15631,15705,15769,15825,15943,16001,16063,16119,16199,16333,16511,16587,16685,16766,16847,16988,17069,17149,17300,17390,17467,17523,17579,17645,17724,17806,17877,17966,18039,18116,18186,18263,18369,18458,18532,18626,18728,18800,18881,18985,19038,19123,19190,19283,19372,19434,19498,19561,19629,19740,19851,19953,20058,20118,20178,20440,20922,20998,21152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\aaf9750c511786fa108e7a3c496402ad\\transformed\\play-services-basement-18.5.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5832", "endColumns": "145", "endOffsets": "5973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\835032a51b5d40afa0ab30aa91122c7e\\transformed\\quickie-bundled-1.10.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,124", "endColumns": "68,69", "endOffsets": "119,189"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "20445,20514", "endColumns": "68,69", "endOffsets": "20509,20579"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\be5cc04b6a4fee62d71666ba2f889b70\\transformed\\appcompat-1.7.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,448,557,665,750,852,968,1053,1133,1224,1317,1412,1506,1605,1698,1797,1893,1984,2075,2157,2264,2363,2462,2570,2678,2785,2944,20761", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "443,552,660,745,847,963,1048,1128,1219,1312,1407,1501,1600,1693,1792,1888,1979,2070,2152,2259,2358,2457,2565,2673,2780,2939,3039,20839"}}]}]}