{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f58a4462c0f661bc016516b04b05ffbf\\transformed\\core-1.16.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "39,40,41,42,43,44,45,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3599,3698,3800,3900,3998,4105,4211,21580", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "3693,3795,3895,3993,4100,4206,4322,21676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9436b4ebc37cac3d97710224e74d8985\\transformed\\play-services-base-18.5.0\\res\\values-gl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,454,579,683,838,966,1081,1186,1353,1458,1623,1754,1915,2063,2126,2191", "endColumns": "101,158,124,103,154,127,114,104,166,104,164,130,160,147,62,64,80", "endOffsets": "294,453,578,682,837,965,1080,1185,1352,1457,1622,1753,1914,2062,2125,2190,2271"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4824,4930,5093,5222,5330,5489,5621,5740,5983,6154,6263,6432,6567,6732,6884,6951,7020", "endColumns": "105,162,128,107,158,131,118,108,170,108,168,134,164,151,66,68,84", "endOffsets": "4925,5088,5217,5325,5484,5616,5735,5844,6149,6258,6427,6562,6727,6879,6946,7015,7100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c0ee106e096d6fe0bd1735cc2a0a4c89\\transformed\\quickie-bundled-1.10.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,113", "endColumns": "57,69", "endOffsets": "108,178"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "20552,20610", "endColumns": "57,69", "endOffsets": "20605,20675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\96abb1e1502d74c75d73c35e14f81e22\\transformed\\play-services-basement-18.5.0\\res\\values-gl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "129", "endOffsets": "324"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5849", "endColumns": "133", "endOffsets": "5978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\ec01753cc085eccabdf318240ae84988\\transformed\\ui-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,282,365,474,575,664,743,836,928,1016,1101,1191,1268,1353,1437,1517,1593,1675,1747", "endColumns": "95,82,108,100,88,78,92,91,87,84,89,76,84,83,79,75,81,71,121", "endOffsets": "277,360,469,570,659,738,831,923,1011,1096,1186,1263,1348,1432,1512,1588,1670,1742,1864"}, "to": {"startLines": "49,50,69,70,71,76,77,204,205,209,210,214,216,217,218,219,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4645,4741,7105,7214,7315,7719,7798,20287,20379,20680,20765,21100,21255,21340,21424,21504,21681,21763,21835", "endColumns": "95,82,108,100,88,78,92,91,87,84,89,76,84,83,79,75,81,71,121", "endOffsets": "4736,4819,7209,7310,7399,7793,7886,20374,20462,20760,20850,21172,21335,21419,21499,21575,21758,21830,21952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\bd9e012c025b7c80daa2bbfa6a34ffed\\transformed\\foundation-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,234", "endColumns": "79,98,100", "endOffsets": "130,229,330"}, "to": {"startLines": "33,224,225", "startColumns": "4,4,4", "startOffsets": "3075,21957,22056", "endColumns": "79,98,100", "endOffsets": "3150,22051,22152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b48892203169544dad7e4ce4c2aaf9a0\\transformed\\material3-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,294,417,537,638,734,847,985,1103,1256,1340,1443,1540,1641,1759,1877,1985,2124,2262,2414,2599,2734,2853,2975,3100,3198,3295,3416,3564,3665,3778,3892,4032,4177,4287,4393,4479,4575,4671,4793,4913,5032,5119,5205,5294,5400,5500,5582,5688,5772,5873,5980,6071,6170,6258,6371,6472,6575,6699,6781,6931,7064,7182,7290,7398", "endColumns": "120,117,122,119,100,95,112,137,117,152,83,102,96,100,117,117,107,138,137,151,184,134,118,121,124,97,96,120,147,100,112,113,139,144,109,105,85,95,95,121,119,118,86,85,88,105,99,81,105,83,100,106,90,98,87,112,100,102,123,81,149,132,117,107,107,112", "endOffsets": "171,289,412,532,633,729,842,980,1098,1251,1335,1438,1535,1636,1754,1872,1980,2119,2257,2409,2594,2729,2848,2970,3095,3193,3290,3411,3559,3660,3773,3887,4027,4172,4282,4388,4474,4570,4666,4788,4908,5027,5114,5200,5289,5395,5495,5577,5683,5767,5868,5975,6066,6165,6253,6366,6467,6570,6694,6776,6926,7059,7177,7285,7393,7506"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7954,8075,8193,8316,8436,8537,8633,8746,8884,9002,9155,9239,9342,9439,9540,9658,9776,9884,10023,10161,10313,10498,10633,10752,10874,10999,11097,11194,11315,11463,11564,11677,11791,11931,12076,12186,12292,12378,12474,12570,12692,12812,12931,13018,13104,13193,13299,13399,13481,13587,13671,13772,13879,13970,14069,14157,14270,14371,14474,14598,14680,14830,14963,15081,15189,15297", "endColumns": "120,117,122,119,100,95,112,137,117,152,83,102,96,100,117,117,107,138,137,151,184,134,118,121,124,97,96,120,147,100,112,113,139,144,109,105,85,95,95,121,119,118,86,85,88,105,99,81,105,83,100,106,90,98,87,112,100,102,123,81,149,132,117,107,107,112", "endOffsets": "8070,8188,8311,8431,8532,8628,8741,8879,8997,9150,9234,9337,9434,9535,9653,9771,9879,10018,10156,10308,10493,10628,10747,10869,10994,11092,11189,11310,11458,11559,11672,11786,11926,12071,12181,12287,12373,12469,12565,12687,12807,12926,13013,13099,13188,13294,13394,13476,13582,13666,13767,13874,13965,14064,14152,14265,14366,14469,14593,14675,14825,14958,15076,15184,15292,15405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9d7219de08abf79892e9add9bb5b00cd\\transformed\\material-1.12.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1099,1177,1273,1352,1415,1510,1574,1643,1706,1780,1844,1900,2021,2079,2141,2197,2274,2413,2501,2578,2674,2758,2838,2978,3058,3138,3287,3377,3458,3514,3570,3636,3715,3796,3867,3955,4034,4111,4193,4282,4383,4467,4559,4652,4753,4827,4919,5021,5073,5157,5223,5315,5403,5465,5529,5592,5662,5773,5878,5984,6083,6143,6203,6288,6371,6450", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,84,101,95,104,132,79,61,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,76,95,83,79,139,79,79,148,89,80,55,55,65,78,80,70,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84,82,78,77", "endOffsets": "270,351,431,516,618,714,819,952,1032,1094,1172,1268,1347,1410,1505,1569,1638,1701,1775,1839,1895,2016,2074,2136,2192,2269,2408,2496,2573,2669,2753,2833,2973,3053,3133,3282,3372,3453,3509,3565,3631,3710,3791,3862,3950,4029,4106,4188,4277,4378,4462,4554,4647,4748,4822,4914,5016,5068,5152,5218,5310,5398,5460,5524,5587,5657,5768,5873,5979,6078,6138,6198,6283,6366,6445,6523"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3155,3236,3316,3401,3503,4327,4432,4565,7404,7466,7544,7640,7891,15410,15505,15569,15638,15701,15775,15839,15895,16016,16074,16136,16192,16269,16497,16585,16662,16758,16842,16922,17062,17142,17222,17371,17461,17542,17598,17654,17720,17799,17880,17951,18039,18118,18195,18277,18366,18467,18551,18643,18736,18837,18911,19003,19105,19157,19241,19307,19399,19487,19549,19613,19676,19746,19857,19962,20068,20167,20227,20467,20938,21021,21177", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "endColumns": "12,80,79,84,101,95,104,132,79,61,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,76,95,83,79,139,79,79,148,89,80,55,55,65,78,80,70,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84,82,78,77", "endOffsets": "320,3231,3311,3396,3498,3594,4427,4560,4640,7461,7539,7635,7714,7949,15500,15564,15633,15696,15770,15834,15890,16011,16069,16131,16187,16264,16403,16580,16657,16753,16837,16917,17057,17137,17217,17366,17456,17537,17593,17649,17715,17794,17875,17946,18034,18113,18190,18272,18361,18462,18546,18638,18731,18832,18906,18998,19100,19152,19236,19302,19394,19482,19544,19608,19671,19741,19852,19957,20063,20162,20222,20282,20547,21016,21095,21250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\757e326fd7991b30514ac12e75a7701a\\transformed\\appcompat-1.7.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,429,533,641,726,827,955,1041,1122,1214,1308,1405,1499,1599,1693,1789,1884,1976,2068,2149,2257,2364,2471,2580,2685,2799,2976,20855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "424,528,636,721,822,950,1036,1117,1209,1303,1400,1494,1594,1688,1784,1879,1971,2063,2144,2252,2359,2466,2575,2680,2794,2971,3070,20933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b9676b1ec2d82f1cef53d671c6d0afde\\transformed\\material-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "16408", "endColumns": "88", "endOffsets": "16492"}}]}]}