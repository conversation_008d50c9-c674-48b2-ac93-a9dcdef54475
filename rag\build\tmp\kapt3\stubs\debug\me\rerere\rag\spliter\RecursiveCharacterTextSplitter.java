package me.rerere.rag.spliter;

/**
 * 递归字符文本分割器
 * 支持多级分割，先尝试按段落分割，如果块太大，再尝试按句子分割，以此类推
 * @param chunkSize 每个文本块的最大大小
 * @param chunkOverlap 相邻文本块之间的重叠大小
 * @param separators 按优先级排序的分隔符列表
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0000\u0018\u00002\u00020\u0001B+\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\u0016\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\u000b\u001a\u00020\u0007H\u0016J\u001e\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\u000b\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\u0003H\u0002J\u0016\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\u000b\u001a\u00020\u0007H\u0002J\u001c\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0002J\u0014\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00130\u0012H\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lme/rerere/rag/spliter/RecursiveCharacterTextSplitter;", "Lme/rerere/rag/spliter/TextSplitter;", "chunkSize", "", "chunkOverlap", "separators", "", "", "<init>", "(IILjava/util/List;)V", "split", "text", "splitText", "level", "splitByCharacters", "mergeWithOverlap", "chunks", "getConfig", "", "", "rag_debug"})
public final class RecursiveCharacterTextSplitter implements me.rerere.rag.spliter.TextSplitter {
    private final int chunkSize = 0;
    private final int chunkOverlap = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> separators = null;
    
    public RecursiveCharacterTextSplitter(int chunkSize, int chunkOverlap, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> separators) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.util.List<java.lang.String> split(@org.jetbrains.annotations.NotNull()
    java.lang.String text) {
        return null;
    }
    
    private final java.util.List<java.lang.String> splitText(java.lang.String text, int level) {
        return null;
    }
    
    private final java.util.List<java.lang.String> splitByCharacters(java.lang.String text) {
        return null;
    }
    
    private final java.util.List<java.lang.String> mergeWithOverlap(java.util.List<java.lang.String> chunks) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.util.Map<java.lang.String, java.lang.Object> getConfig() {
        return null;
    }
    
    public RecursiveCharacterTextSplitter() {
        super();
    }
}