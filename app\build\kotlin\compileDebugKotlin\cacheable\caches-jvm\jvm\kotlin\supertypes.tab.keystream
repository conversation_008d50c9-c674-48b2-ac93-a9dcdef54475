=me.rerere.rikkahub.data.db.AppDatabase_AutoMigration_1_2_Impl=me.rerere.rikkahub.data.db.AppDatabase_AutoMigration_2_3_Impl=me.rerere.rikkahub.data.db.AppDatabase_AutoMigration_3_4_Impl=me.rerere.rikkahub.data.db.AppDatabase_AutoMigration_4_5_Impl+me.rerere.rikkahub.data.db.AppDatabase_Impl3me.rerere.rikkahub.data.db.dao.ConversationDAO_Impl-me.rerere.rikkahub.data.db.dao.MemoryDAO_Implme.rerere.rikkahub.RikkaHubAppme.rerere.rikkahub.AppScope me.rerere.rikkahub.RouteActivity<me.rerere.rikkahub.data.ai.Base64ImageToLocalFileTransformer3me.rerere.rikkahub.data.ai.GenerationChunk.Messages5me.rerere.rikkahub.data.ai.GenerationChunk.TokenUsage6me.rerere.rikkahub.data.datastore.Settings.$serializer<me.rerere.rikkahub.data.datastore.DisplaySetting.$serializer&me.rerere.rikkahub.data.db.AppDatabase8me.rerere.rikkahub.data.mcp.McpCommonOptions.$serializer/me.rerere.rikkahub.data.mcp.McpTool.$serializer>me.rerere.rikkahub.data.mcp.McpServerConfig.SseTransportServerJme.rerere.rikkahub.data.mcp.McpServerConfig.SseTransportServer.$serializer;me.rerere.rikkahub.data.mcp.McpServerConfig.WebSocketServerGme.rerere.rikkahub.data.mcp.McpServerConfig.WebSocketServer.$serializer8me.rerere.rikkahub.data.mcp.transport.SseClientTransport3me.rerere.rikkahub.data.model.Assistant.$serializer9me.rerere.rikkahub.data.model.AssistantMemory.$serializer6me.rerere.rikkahub.data.model.Conversation.$serializer'<EMAIL>.$serializer9me.rerere.rikkahub.ui.components.chat.ChatInputStateSaver1me.rerere.rikkahub.ui.components.chat.ExpandStateKme.rerere.rikkahub.ui.components.richtext.HighlightCodeVisualTransformation6me.rerere.rikkahub.ui.components.richtext.MermaidTheme;me.rerere.rikkahub.ui.components.table.ColumnWidth.Adaptive8me.rerere.rikkahub.ui.components.table.ColumnWidth.Fixed+me.rerere.rikkahub.ui.components.ui.TagType:me.rerere.rikkahub.ui.components.webview.MyWebChromeClient8me.rerere.rikkahub.ui.components.webview.MyWebViewClient7me.rerere.rikkahub.ui.components.webview.WebContent.Url8me.rerere.rikkahub.ui.components.webview.WebContent.DataAme.rerere.rikkahub.ui.components.webview.WebContent.NavigatorOnly,me.rerere.rikkahub.ui.hooks.tts.TtsStateImpl1me.rerere.rikkahub.ui.pages.assistant.AssistantVM>me.rerere.rikkahub.ui.pages.assistant.detail.AssistantDetailVM'me.rerere.rikkahub.ui.pages.chat.ChatVM)me.rerere.rikkahub.ui.pages.debug.DebugVM-me.rerere.rikkahub.ui.pages.history.HistoryVM7me.rerere.rikkahub.ui.pages.setting.ProviderExpandState-me.rerere.rikkahub.ui.pages.setting.SettingVM3me.rerere.rikkahub.ui.pages.translator.TranslatorVM+me.rerere.rikkahub.ui.theme.PresetThemeType%me.rerere.rikkahub.ui.theme.ColorMode(me.rerere.rikkahub.utils.UiState.Loading(me.rerere.rikkahub.utils.UiState.Success&me.rerere.rikkahub.utils.UiState.Error3me.rerere.rikkahub.utils.UpdateDownload.$serializer/me.rerere.rikkahub.utils.UpdateInfo.$serializer me.rerere.rikkahub.utils.Version                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     