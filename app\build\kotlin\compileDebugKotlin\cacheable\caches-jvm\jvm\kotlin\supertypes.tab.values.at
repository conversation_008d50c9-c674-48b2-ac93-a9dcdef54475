/ Header Record For PersistentHashMapValueStorage" !androidx.room.migration.Migration" !androidx.room.migration.Migration" !androidx.room.migration.Migration" !androidx.room.migration.Migration' &me.rerere.rikkahub.data.db.AppDatabase/ .me.rerere.rikkahub.data.db.dao.ConversationDAO) (me.rerere.rikkahub.data.db.dao.MemoryDAO android.app.Application" !kotlinx.coroutines.CoroutineScope$ #androidx.activity.ComponentActivity) (me.rerere.ai.ui.OutputMessageTransformer+ *me.rerere.rikkahub.data.ai.GenerationChunk+ *me.rerere.rikkahub.data.ai.GenerationChunk3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer androidx.room.RoomDatabase3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer, +me.rerere.rikkahub.data.mcp.McpServerConfig3 2kotlinx.serialization.internal.GeneratedSerializer, +me.rerere.rikkahub.data.mcp.McpServerConfig3 2kotlinx.serialization.internal.GeneratedSerializer< ;io.modelcontextprotocol.kotlin.sdk.shared.AbstractTransport3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer android.app.Service android.os.Binder3 2kotlinx.serialization.internal.GeneratedSerializer( 'androidx.compose.runtime.saveable.Saver kotlin.Enum4 3androidx.compose.ui.text.input.VisualTransformation kotlin.Enum3 2me.rerere.rikkahub.ui.components.table.ColumnWidth3 2me.rerere.rikkahub.ui.components.table.ColumnWidth kotlin.Enum android.webkit.WebChromeClient android.webkit.WebViewClient4 3me.rerere.rikkahub.ui.components.webview.WebContent4 3me.rerere.rikkahub.ui.components.webview.WebContent4 3me.rerere.rikkahub.ui.components.webview.WebContentX (me.rerere.rikkahub.ui.hooks.tts.TtsState.android.speech.tts.TextToSpeech.OnInitListener androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum!  me.rerere.rikkahub.utils.UiState!  me.rerere.rikkahub.utils.UiState!  me.rerere.rikkahub.utils.UiState3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Comparable