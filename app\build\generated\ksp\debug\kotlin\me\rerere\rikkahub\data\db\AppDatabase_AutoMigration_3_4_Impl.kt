package me.rerere.rikkahub.`data`.db

import androidx.room.migration.Migration
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL
import javax.`annotation`.processing.Generated
import kotlin.Suppress

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
internal class AppDatabase_AutoMigration_3_4_Impl : Migration {
  public constructor() : super(3, 4)

  public override fun migrate(connection: SQLiteConnection) {
    connection.execSQL("ALTER TABLE `ConversationEntity` ADD COLUMN `usage` TEXT DEFAULT NULL")
  }
}
