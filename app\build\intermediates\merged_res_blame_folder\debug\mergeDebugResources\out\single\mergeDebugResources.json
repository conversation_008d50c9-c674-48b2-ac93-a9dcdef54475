[{"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-mdpi_ic_launcher_monochrome.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-mdpi/ic_launcher_monochrome.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xhdpi_ic_launcher_foreground.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xhdpi/ic_launcher_foreground.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-hdpi_ic_launcher_monochrome.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-hdpi/ic_launcher_monochrome.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/xml__generated_res_locale_config.xml.flat", "source": "me.rerere.rikkahub.app-localeConfig-104:/xml/_generated_res_locale_config.xml"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xxxhdpi_ic_launcher_background.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xxxhdpi/ic_launcher_background.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xhdpi_ic_launcher_monochrome.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xhdpi/ic_launcher_monochrome.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-hdpi_ic_launcher_foreground.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-hdpi/ic_launcher_foreground.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xhdpi_ic_launcher.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-mdpi_ic_launcher.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-mdpi/ic_launcher.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xhdpi_ic_launcher_background.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xhdpi/ic_launcher_background.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/xml_file_paths.xml.flat", "source": "me.rerere.rikkahub.app-main-112:/xml/file_paths.xml"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-mdpi_ic_launcher_background.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-mdpi/ic_launcher_background.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-mdpi_ic_launcher_foreground.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-mdpi/ic_launcher_foreground.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xxhdpi_ic_launcher_foreground.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xxhdpi/ic_launcher_foreground.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xxxhdpi_ic_launcher_monochrome.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xxxhdpi/ic_launcher_monochrome.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-hdpi_ic_launcher_background.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-hdpi/ic_launcher_background.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/xml_data_extraction_rules.xml.flat", "source": "me.rerere.rikkahub.app-main-112:/xml/data_extraction_rules.xml"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xxxhdpi_ic_launcher_foreground.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xxxhdpi/ic_launcher_foreground.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/xml_backup_rules.xml.flat", "source": "me.rerere.rikkahub.app-main-112:/xml/backup_rules.xml"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xxhdpi_ic_launcher_background.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xxhdpi/ic_launcher_background.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-xxhdpi_ic_launcher_monochrome.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-xxhdpi/ic_launcher_monochrome.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/mipmap-hdpi_ic_launcher.png.flat", "source": "me.rerere.rikkahub.app-main-112:/mipmap-hdpi/ic_launcher.png"}, {"merged": "me.rerere.rikkahub.app-debug-110:/drawable_small_icon.xml.flat", "source": "me.rerere.rikkahub.app-main-112:/drawable/small_icon.xml"}]