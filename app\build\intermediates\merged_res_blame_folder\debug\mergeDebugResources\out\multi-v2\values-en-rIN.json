{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d642686678dc9e4dc645c444fc2595b8\\transformed\\core-1.16.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "30,31,32,33,34,35,36,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2837,2933,3035,3134,3233,3337,3440,12140", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "2928,3030,3129,3228,3332,3435,3551,12236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4e69add8b1376897dea52ac0f7720f60\\transformed\\ui-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,273,355,449,548,635,717,806,895,979,1057,1139,1212,1296,1372,1444,1514,1591,1657", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,83,75,71,69,76,65,120", "endOffsets": "268,350,444,543,630,712,801,890,974,1052,1134,1207,1291,1367,1439,1509,1586,1652,1773"}, "to": {"startLines": "37,38,39,40,41,42,43,111,112,113,114,116,117,118,119,120,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3556,3648,3730,3824,3923,4010,4092,11349,11438,11522,11600,11765,11838,11922,11998,12070,12241,12318,12384", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,83,75,71,69,76,65,120", "endOffsets": "3643,3725,3819,3918,4005,4087,4176,11433,11517,11595,11677,11833,11917,11993,12065,12135,12313,12379,12500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2f477ab85e7d6008688a1a0c4b191573\\transformed\\material-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "11261", "endColumns": "87", "endOffsets": "11344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\be5cc04b6a4fee62d71666ba2f889b70\\transformed\\appcompat-1.7.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,11682", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,11760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8bf40832b048d1f4d2354372c996192d\\transformed\\material3-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2466,2587,2703,2826,2951,3043,3141,3258,3382,3479,3581,3683,3813,3952,4058,4157,4235,4331,4425,4530,4635,4736,4823,4910,4998,5100,5196,5278,5377,5461,5562,5663,5763,5862,5950,6056,6157,6261,6381,6463,6598,6725,6825,6920,7025", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,104,100,86,86,87,101,95,81,98,83,100,100,99,98,87,105,100,103,119,81,134,126,99,94,104,109", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2461,2582,2698,2821,2946,3038,3136,3253,3377,3474,3576,3678,3808,3947,4053,4152,4230,4326,4420,4525,4630,4731,4818,4905,4993,5095,5191,5273,5372,5456,5557,5658,5758,5857,5945,6051,6152,6256,6376,6458,6593,6720,6820,6915,7020,7130"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4181,4299,4415,4526,4640,4739,4834,4946,5082,5198,5334,5418,5517,5608,5705,5824,5949,6053,6180,6303,6431,6592,6713,6829,6952,7077,7169,7267,7384,7508,7605,7707,7809,7939,8078,8184,8283,8361,8457,8551,8656,8761,8862,8949,9036,9124,9226,9322,9404,9503,9587,9688,9789,9889,9988,10076,10182,10283,10387,10507,10589,10724,10851,10951,11046,11151", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,104,100,86,86,87,101,95,81,98,83,100,100,99,98,87,105,100,103,119,81,134,126,99,94,104,109", "endOffsets": "4294,4410,4521,4635,4734,4829,4941,5077,5193,5329,5413,5512,5603,5700,5819,5944,6048,6175,6298,6426,6587,6708,6824,6947,7072,7164,7262,7379,7503,7600,7702,7804,7934,8073,8179,8278,8356,8452,8546,8651,8756,8857,8944,9031,9119,9221,9317,9399,9498,9582,9683,9784,9884,9983,10071,10177,10278,10382,10502,10584,10719,10846,10946,11041,11146,11256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\303c2166503144bea901100ae0f040af\\transformed\\foundation-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,216", "endColumns": "74,85,84", "endOffsets": "125,211,296"}, "to": {"startLines": "29,125,126", "startColumns": "4,4,4", "startOffsets": "2762,12505,12591", "endColumns": "74,85,84", "endOffsets": "2832,12586,12671"}}]}]}