package me.rerere.rag.db;

/**
 * 向量数据库接口，用于存储和检索向量数据
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0014\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\bf\u0018\u00002\u00020\u0001J,\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0007H&J(\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\f\u001a\u00020\n2\b\b\u0002\u0010\r\u001a\u00020\u000eH&J\u0018\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0011\u001a\u00020\u0005H&J\u0016\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0014\u001a\u00020\u0003H&\u00a8\u0006\u0015"}, d2 = {"Lme/rerere/rag/db/VectorDatabase;", "", "addDocuments", "", "table", "", "document", "", "Lme/rerere/rag/document/Document;", "vectors", "", "search", "embedding", "limit", "", "deleteDocument", "", "id", "getAllDocuments", "clear", "close", "rag_debug"})
public abstract interface VectorDatabase {
    
    /**
     * 添加文档到向量数据库
     *
     * @param table 表名
     * @param document 文档列表
     * @param vectors 向量列表
     */
    public abstract void addDocuments(@org.jetbrains.annotations.NotNull()
    java.lang.String table, @org.jetbrains.annotations.NotNull()
    java.util.List<me.rerere.rag.document.Document> document, @org.jetbrains.annotations.NotNull()
    java.util.List<float[]> vectors);
    
    /**
     * 根据向量相似度搜索最相似的文档
     *
     * @param table 数据表名
     * @param embedding 查询向量
     * @param limit 返回结果的最大数量
     * @return 相似文档列表
     */
    @org.jetbrains.annotations.NotNull()
    public abstract java.util.List<me.rerere.rag.document.Document> search(@org.jetbrains.annotations.NotNull()
    java.lang.String table, @org.jetbrains.annotations.NotNull()
    float[] embedding, int limit);
    
    /**
     * 根据ID删除文档
     *
     * @param table 数据表名
     * @param id 文档ID
     */
    public abstract boolean deleteDocument(@org.jetbrains.annotations.NotNull()
    java.lang.String table, @org.jetbrains.annotations.NotNull()
    java.lang.String id);
    
    /**
     * 获取所有文档
     *
     * @param table 数据表名
     * @return 所有文档列表
     */
    @org.jetbrains.annotations.NotNull()
    public abstract java.util.List<me.rerere.rag.document.Document> getAllDocuments(@org.jetbrains.annotations.NotNull()
    java.lang.String table);
    
    /**
     * 清空数据表
     *
     * @param table 数据表名
     */
    public abstract void clear(@org.jetbrains.annotations.NotNull()
    java.lang.String table);
    
    /**
     * 关闭数据库连接
     */
    public abstract void close();
    
    /**
     * 向量数据库接口，用于存储和检索向量数据
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}