me.rerere.ai.core.MessageRole"me.rerere.ai.core.Tool.$serializer"me.rerere.ai.core.Schema.RawSchema.me.rerere.ai.core.Schema.RawSchema.$serializer%me.rerere.ai.core.Schema.ObjectSchema1me.rerere.ai.core.Schema.ObjectSchema.$serializer$me.rerere.ai.core.Schema.ArraySchema0me.rerere.ai.core.Schema.ArraySchema.$serializer%me.rerere.ai.core.Schema.StringSchema1me.rerere.ai.core.Schema.StringSchema.$serializer%me.rerere.ai.core.Schema.NumberSchema1me.rerere.ai.core.Schema.NumberSchema.$serializer&me.rerere.ai.core.Schema.IntegerSchema2me.rerere.ai.core.Schema.IntegerSchema.$serializer&me.rerere.ai.core.Schema.BooleanSchema#me.rerere.ai.core.Schema.NullSchema#me.rerere.ai.core.Schema.EnumSchema/me.rerere.ai.core.Schema.EnumSchema.$serializer$me.rerere.ai.core.Schema.AnyOfSchema0me.rerere.ai.core.Schema.AnyOfSchema.$serializer$me.rerere.ai.core.Schema.AllOfSchema0me.rerere.ai.core.Schema.AllOfSchema.$serializer(me.rerere.ai.core.TokenUsage.$serializer'me.rerere.ai.provider.Model.$serializerme.rerere.ai.provider.ModelTypeme.rerere.ai.provider.Modality"me.rerere.ai.provider.ModelAbility6me.rerere.ai.provider.TextGenerationParams.$serializer.me.rerere.ai.provider.CustomHeader.$serializer,me.rerere.ai.provider.CustomBody.$serializer,me.rerere.ai.provider.ProviderSetting.OpenAI8me.rerere.ai.provider.ProviderSetting.OpenAI.$serializer,me.rerere.ai.provider.ProviderSetting.Google8me.rerere.ai.provider.ProviderSetting.Google.$serializer.me.rerere.ai.provider.providers.GoogleProvider.me.rerere.ai.provider.providers.OpenAIProvider%me.rerere.ai.ui.UIMessage.$serializer"me.rerere.ai.ui.UIMessagePart.Text.me.rerere.ai.ui.UIMessagePart.Text.$serializer#me.rerere.ai.ui.UIMessagePart.Image/me.rerere.ai.ui.UIMessagePart.Image.$serializer'me.rerere.ai.ui.UIMessagePart.Reasoning3me.rerere.ai.ui.UIMessagePart.Reasoning.$serializer$me.rerere.ai.ui.UIMessagePart.Search&me.rerere.ai.ui.UIMessagePart.ToolCall2me.rerere.ai.ui.UIMessagePart.ToolCall.$serializer(me.rerere.ai.ui.UIMessagePart.ToolResult4me.rerere.ai.ui.UIMessagePart.ToolResult.$serializer/me.rerere.ai.ui.UIMessageAnnotation.UrlCitation;me.rerere.ai.ui.UIMessageAnnotation.UrlCitation.$serializer(me.rerere.ai.ui.MessageChunk.$serializer+me.rerere.ai.ui.UIMessageChoice.$serializer'me.rerere.ai.ui.InputMessageTransformer(me.rerere.ai.ui.OutputMessageTransformer3me.rerere.ai.ui.transformers.MessageTimeTransformer3me.rerere.ai.ui.transformers.PlaceholderTransformer0me.rerere.ai.ui.transformers.ThinkTagTransformerme.rerere.ai.util.HttpException#me.rerere.ai.util.InstantSerializer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      