{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b26bc0a945bb328544f80db00c932160\\transformed\\material-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3239,3328,3412,3502,4315,4416,4538,7439,7501,7567,7661,7908,15387,15495,15561,15630,15688,15760,15824,15878,16006,16066,16128,16182,16260,16491,16583,16661,16755,16841,16925,17070,17154,17240,17373,17463,17542,17599,17650,17716,17790,17872,17943,18018,18092,18170,18242,18316,18426,18518,18600,18689,18778,18852,18930,19016,19071,19150,19217,19297,19381,19443,19507,19570,19639,19746,19853,19952,20058,20119,20351,20811,20894,21046", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "328,3234,3323,3407,3497,3579,4411,4533,4614,7496,7562,7656,7726,7962,15490,15556,15625,15683,15755,15819,15873,16001,16061,16123,16177,16255,16392,16578,16656,16750,16836,16920,17065,17149,17235,17368,17458,17537,17594,17645,17711,17785,17867,17938,18013,18087,18165,18237,18311,18421,18513,18595,18684,18773,18847,18925,19011,19066,19145,19212,19292,19376,19438,19502,19565,19634,19741,19848,19947,20053,20114,20169,20428,20889,20966,21117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\aaf9750c511786fa108e7a3c496402ad\\transformed\\play-services-basement-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5865", "endColumns": "144", "endOffsets": "6005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d642686678dc9e4dc645c444fc2595b8\\transformed\\core-1.16.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "39,40,41,42,43,44,45,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3584,3682,3784,3884,3984,4092,4197,21429", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3677,3779,3879,3979,4087,4192,4310,21525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4e69add8b1376897dea52ac0f7720f60\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,290,378,476,576,663,748,840,929,1017,1098,1182,1257,1347,1422,1494,1564,1643,1709", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,89,74,71,69,78,65,119", "endOffsets": "285,373,471,571,658,743,835,924,1012,1093,1177,1252,1342,1417,1489,1559,1638,1704,1824"}, "to": {"startLines": "49,50,69,70,71,76,77,204,205,209,210,214,216,217,218,219,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4619,4715,7154,7252,7352,7731,7816,20174,20263,20564,20645,20971,21122,21212,21287,21359,21530,21609,21675", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,89,74,71,69,78,65,119", "endOffsets": "4710,4798,7247,7347,7434,7811,7903,20258,20346,20640,20724,21041,21207,21282,21354,21424,21604,21670,21790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2f477ab85e7d6008688a1a0c4b191573\\transformed\\material-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "16397", "endColumns": "93", "endOffsets": "16486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\be5cc04b6a4fee62d71666ba2f889b70\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,20729", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,20806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\303c2166503144bea901100ae0f040af\\transformed\\foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,230", "endColumns": "87,86,89", "endOffsets": "138,225,315"}, "to": {"startLines": "33,224,225", "startColumns": "4,4,4", "startOffsets": "3060,21795,21882", "endColumns": "87,86,89", "endOffsets": "3143,21877,21967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5aea9c81c7615102d7b5640b77daf851\\transformed\\uCrop-n-Edit-4.1.0-non-native\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,163,214,280,338,394,444,499,549", "endColumns": "55,51,50,65,57,55,49,54,49,51", "endOffsets": "106,158,209,275,333,389,439,494,544,596"}, "to": {"startLines": "226,227,228,229,230,231,232,233,234,235", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "21972,22028,22080,22131,22197,22255,22311,22361,22416,22466", "endColumns": "55,51,50,65,57,55,49,54,49,51", "endOffsets": "22023,22075,22126,22192,22250,22306,22356,22411,22461,22513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\835032a51b5d40afa0ab30aa91122c7e\\transformed\\quickie-bundled-1.10.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,119", "endColumns": "63,66", "endOffsets": "114,181"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "20433,20497", "endColumns": "63,66", "endOffsets": "20492,20559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8bf40832b048d1f4d2354372c996192d\\transformed\\material3-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,315,424,553,663,758,870,1014,1132,1288,1373,1478,1573,1675,1793,1919,2029,2165,2302,2437,2616,2744,2867,2995,3120,3216,3314,3434,3563,3663,3768,3870,4011,4159,4265,4367,4447,4543,4638,4758,4870,4980,5066,5155,5249,5350,5452,5532,5637,5723,5823,5929,6024,6125,6213,6322,6423,6527,6665,6754,6904,7047,7152,7248,7361", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,111,109,85,88,93,100,101,79,104,85,99,105,94,100,87,108,100,103,137,88,149,142,104,95,112,113", "endOffsets": "181,310,419,548,658,753,865,1009,1127,1283,1368,1473,1568,1670,1788,1914,2024,2160,2297,2432,2611,2739,2862,2990,3115,3211,3309,3429,3558,3658,3763,3865,4006,4154,4260,4362,4442,4538,4633,4753,4865,4975,5061,5150,5244,5345,5447,5527,5632,5718,5818,5924,6019,6120,6208,6317,6418,6522,6660,6749,6899,7042,7147,7243,7356,7470"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7967,8098,8227,8336,8465,8575,8670,8782,8926,9044,9200,9285,9390,9485,9587,9705,9831,9941,10077,10214,10349,10528,10656,10779,10907,11032,11128,11226,11346,11475,11575,11680,11782,11923,12071,12177,12279,12359,12455,12550,12670,12782,12892,12978,13067,13161,13262,13364,13444,13549,13635,13735,13841,13936,14037,14125,14234,14335,14439,14577,14666,14816,14959,15064,15160,15273", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,111,109,85,88,93,100,101,79,104,85,99,105,94,100,87,108,100,103,137,88,149,142,104,95,112,113", "endOffsets": "8093,8222,8331,8460,8570,8665,8777,8921,9039,9195,9280,9385,9480,9582,9700,9826,9936,10072,10209,10344,10523,10651,10774,10902,11027,11123,11221,11341,11470,11570,11675,11777,11918,12066,12172,12274,12354,12450,12545,12665,12777,12887,12973,13062,13156,13257,13359,13439,13544,13630,13730,13836,13931,14032,14120,14229,14330,14434,14572,14661,14811,14954,15059,15155,15268,15382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d2e6b12382a4dadf34d77e521366ccff\\transformed\\play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4803,4912,5076,5204,5316,5494,5625,5746,6010,6190,6302,6471,6602,6764,6940,7011,7074", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "4907,5071,5199,5311,5489,5620,5741,5860,6185,6297,6466,6597,6759,6935,7006,7069,7149"}}]}]}