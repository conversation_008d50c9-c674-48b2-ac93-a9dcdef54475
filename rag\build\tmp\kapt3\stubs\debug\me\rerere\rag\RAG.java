package me.rerere.rag;

/**
 * RAG（检索增强生成）系统核心类
 * 用于管理向量数据库和文本处理流程
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\u0018\u0000 \u00102\u00020\u0001:\u0003\u000e\u000f\u0010B)\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\u0006\u0010\f\u001a\u00020\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lme/rerere/rag/RAG;", "", "context", "Landroid/content/Context;", "textSplitter", "Lme/rerere/rag/spliter/TextSplitter;", "db", "Lme/rerere/rag/db/VectorDatabase;", "embeddingProvider", "Lme/rerere/rag/RAG$EmbeddingProvider;", "<init>", "(Landroid/content/Context;Lme/rerere/rag/spliter/TextSplitter;Lme/rerere/rag/db/VectorDatabase;Lme/rerere/rag/RAG$EmbeddingProvider;)V", "close", "", "EmbeddingProvider", "Builder", "Companion", "rag_debug"})
public final class RAG {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final me.rerere.rag.spliter.TextSplitter textSplitter = null;
    @org.jetbrains.annotations.NotNull()
    private final me.rerere.rag.db.VectorDatabase db = null;
    @org.jetbrains.annotations.NotNull()
    private final me.rerere.rag.RAG.EmbeddingProvider embeddingProvider = null;
    @org.jetbrains.annotations.NotNull()
    public static final me.rerere.rag.RAG.Companion Companion = null;
    
    private RAG(android.content.Context context, me.rerere.rag.spliter.TextSplitter textSplitter, me.rerere.rag.db.VectorDatabase db, me.rerere.rag.RAG.EmbeddingProvider embeddingProvider) {
        super();
    }
    
    /**
     * 关闭RAG系统，释放资源
     */
    public final void close() {
    }
    
    /**
     * RAG系统构建器类
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u000e\u0010\f\u001a\u00020\u00002\u0006\u0010\r\u001a\u00020\u0007J\u000e\u0010\u000e\u001a\u00020\u00002\u0006\u0010\u000f\u001a\u00020\tJ\u000e\u0010\u0010\u001a\u00020\u00002\u0006\u0010\u0011\u001a\u00020\u000bJ\u0006\u0010\u0012\u001a\u00020\u0013R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lme/rerere/rag/RAG$Builder;", "", "context", "Landroid/content/Context;", "<init>", "(Landroid/content/Context;)V", "textSplitter", "Lme/rerere/rag/spliter/TextSplitter;", "db", "Lme/rerere/rag/db/VectorDatabase;", "embeddingProvider", "Lme/rerere/rag/RAG$EmbeddingProvider;", "setTextSplitter", "splitter", "setVectorDatabase", "database", "setEmbeddingProvider", "provider", "build", "Lme/rerere/rag/RAG;", "rag_debug"})
    public static final class Builder {
        @org.jetbrains.annotations.NotNull()
        private final android.content.Context context = null;
        @org.jetbrains.annotations.Nullable()
        private me.rerere.rag.spliter.TextSplitter textSplitter;
        @org.jetbrains.annotations.Nullable()
        private me.rerere.rag.db.VectorDatabase db;
        @org.jetbrains.annotations.Nullable()
        private me.rerere.rag.RAG.EmbeddingProvider embeddingProvider;
        
        public Builder(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            super();
        }
        
        /**
         * 设置文本分割器
         */
        @org.jetbrains.annotations.NotNull()
        public final me.rerere.rag.RAG.Builder setTextSplitter(@org.jetbrains.annotations.NotNull()
        me.rerere.rag.spliter.TextSplitter splitter) {
            return null;
        }
        
        /**
         * 设置向量数据库
         */
        @org.jetbrains.annotations.NotNull()
        public final me.rerere.rag.RAG.Builder setVectorDatabase(@org.jetbrains.annotations.NotNull()
        me.rerere.rag.db.VectorDatabase database) {
            return null;
        }
        
        /**
         * 设置向量嵌入提供者
         */
        @org.jetbrains.annotations.NotNull()
        public final me.rerere.rag.RAG.Builder setEmbeddingProvider(@org.jetbrains.annotations.NotNull()
        me.rerere.rag.RAG.EmbeddingProvider provider) {
            return null;
        }
        
        /**
         * 构建RAG实例
         * @throws IllegalStateException 如果缺少必要组件
         */
        @org.jetbrains.annotations.NotNull()
        public final me.rerere.rag.RAG build() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u000e\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007\u00a8\u0006\b"}, d2 = {"Lme/rerere/rag/RAG$Companion;", "", "<init>", "()V", "builder", "Lme/rerere/rag/RAG$Builder;", "context", "Landroid/content/Context;", "rag_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 创建新的RAG构建器
         */
        @org.jetbrains.annotations.NotNull()
        public final me.rerere.rag.RAG.Builder builder(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
    
    /**
     * 向量嵌入提供者接口
     * 负责将文本转换为向量嵌入
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0014\n\u0000\n\u0002\u0010\u000e\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0006"}, d2 = {"Lme/rerere/rag/RAG$EmbeddingProvider;", "", "embed", "", "text", "", "rag_debug"})
    public static abstract interface EmbeddingProvider {
        
        /**
         * 将文本转换为向量嵌入
         * @param text 要嵌入的文本
         * @return 向量嵌入（浮点数数组）
         */
        @org.jetbrains.annotations.NotNull()
        public abstract float[] embed(@org.jetbrains.annotations.NotNull()
        java.lang.String text);
    }
}