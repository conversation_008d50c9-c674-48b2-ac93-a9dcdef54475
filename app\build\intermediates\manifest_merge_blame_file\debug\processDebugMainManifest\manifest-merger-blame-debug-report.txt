1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="me.rerere.rikkahub.debug"
4    android:versionCode="45"
5    android:versionName="0.7.19" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.CAMERA" />
12-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:6:5-65
12-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:7:22-74
14
15    <uses-feature
15-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:9:5-11:36
16        android:name="android.hardware.camera"
16-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:10:9-47
17        android:required="false" />
17-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:11:9-33
18    <uses-feature
18-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:12:5-14:36
19        android:name="android.hardware.camera.autofocus"
19-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:13:9-57
20        android:required="false" />
20-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:14:9-33
21
22    <queries>
22-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:74:5-78:15
23        <intent>
23-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:75:9-77:18
24            <action android:name="android.intent.action.TTS_SERVICE" />
24-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:76:13-72
24-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:76:21-69
25        </intent>
26    </queries>
27
28    <uses-permission android:name="android.permission.WAKE_LOCK" />
28-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
28-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
29-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:22-76
30    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
30-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
30-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
31-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
32    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
33    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
34    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
35    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
35-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
35-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
36    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
36-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
36-->[com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:22-76
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
37-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
37-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
38    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
38-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
38-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
39
40    <permission
40-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
41        android:name="me.rerere.rikkahub.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="me.rerere.rikkahub.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
45
46    <application
46-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:16:5-72:19
47        android:name="me.rerere.rikkahub.RikkaHubApp"
47-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:17:9-36
48        android:allowBackup="true"
48-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:18:9-35
49        android:appCategory="productivity"
49-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:19:9-43
50        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
50-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
51        android:dataExtractionRules="@xml/data_extraction_rules"
51-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:20:9-65
52        android:debuggable="true"
53        android:enableOnBackInvokedCallback="true"
53-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:21:9-51
54        android:extractNativeLibs="false"
55        android:fullBackupContent="@xml/backup_rules"
55-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:22:9-54
56        android:icon="@mipmap/ic_launcher"
56-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:23:9-43
57        android:label="@string/app_name"
57-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:24:9-41
58        android:localeConfig="@xml/_generated_res_locale_config"
59        android:supportsRtl="true"
59-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:25:9-35
60        android:testOnly="true"
61        android:theme="@style/Theme.Rikkahub"
61-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:26:9-46
62        android:usesCleartextTraffic="true" >
62-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:27:9-44
63        <activity
63-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:29:9-40:20
64            android:name="me.rerere.rikkahub.RouteActivity"
64-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:30:13-42
65            android:configChanges="keyboardHidden|orientation|screenSize"
65-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:31:13-74
66            android:exported="true"
66-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:32:13-36
67            android:theme="@style/Theme.Rikkahub"
67-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:33:13-50
68            android:windowSoftInputMode="adjustResize" >
68-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:34:13-55
69            <intent-filter>
69-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:35:13-39:29
70                <action android:name="android.intent.action.MAIN" />
70-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:36:17-69
70-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:36:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:38:17-77
72-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:38:27-74
73            </intent-filter>
74        </activity>
75        <activity
75-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:42:9-44:72
76            android:name="com.yalantis.ucrop.UCropActivity"
76-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:43:13-60
77            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
77-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:44:13-69
78
79        <service
79-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:46:9-50:15
80            android:name="me.rerere.rikkahub.services.ChatService"
80-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:47:13-49
81            android:enabled="true"
81-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:48:13-35
82            android:foregroundServiceType="remoteMessaging" />
82-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:49:13-60
83
84        <provider
85            android:name="androidx.core.content.FileProvider"
85-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:53:13-62
86            android:authorities="me.rerere.rikkahub.debug.fileprovider"
86-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:54:13-64
87            android:exported="false"
87-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:55:13-37
88            android:grantUriPermissions="true" >
88-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:56:13-47
89            <meta-data
89-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:57:13-59:54
90                android:name="android.support.FILE_PROVIDER_PATHS"
90-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:58:17-67
91                android:resource="@xml/file_paths" />
91-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:59:17-51
92        </provider>
93        <provider
94            android:name="androidx.startup.InitializationProvider"
94-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:63:13-67
95            android:authorities="me.rerere.rikkahub.debug.androidx-startup"
95-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:64:13-68
96            android:exported="false" >
96-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:65:13-37
97            <meta-data
97-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.emoji2.text.EmojiCompatInitializer"
98-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
99                android:value="androidx.startup" />
99-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
100            <meta-data
100-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
101                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
101-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
102                android:value="androidx.startup" />
102-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
103            <meta-data
103-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
104-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
105                android:value="androidx.startup" />
105-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
106        </provider>
107
108        <service
108-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
109            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
109-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
110            android:directBootAware="false"
110-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
111            android:enabled="@bool/enable_system_alarm_service_default"
111-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
112            android:exported="false" />
112-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
113        <service
113-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
114            android:name="androidx.work.impl.background.systemjob.SystemJobService"
114-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
115            android:directBootAware="false"
115-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
116            android:enabled="@bool/enable_system_job_service_default"
116-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
117            android:exported="true"
117-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
118            android:permission="android.permission.BIND_JOB_SERVICE" />
118-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
119        <service
119-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
120            android:name="androidx.work.impl.foreground.SystemForegroundService"
120-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
121            android:directBootAware="false"
121-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
122            android:enabled="@bool/enable_system_foreground_service_default"
122-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
123            android:exported="false" />
123-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
124
125        <receiver
125-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
126            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
126-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
127            android:directBootAware="false"
127-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
128            android:enabled="true"
128-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
129            android:exported="false" />
129-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
130        <receiver
130-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
131            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
131-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
132            android:directBootAware="false"
132-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
133            android:enabled="false"
133-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
134            android:exported="false" >
134-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
135            <intent-filter>
135-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
136                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
136-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
136-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
137                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
137-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
137-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
138            </intent-filter>
139        </receiver>
140        <receiver
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
141            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
142            android:directBootAware="false"
142-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
143            android:enabled="false"
143-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
144            android:exported="false" >
144-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
145            <intent-filter>
145-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
146                <action android:name="android.intent.action.BATTERY_OKAY" />
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
147                <action android:name="android.intent.action.BATTERY_LOW" />
147-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
147-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
148            </intent-filter>
149        </receiver>
150        <receiver
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
151            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
152            android:directBootAware="false"
152-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
153            android:enabled="false"
153-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
154            android:exported="false" >
154-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
155            <intent-filter>
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
156                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
157                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
157-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
157-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
158            </intent-filter>
159        </receiver>
160        <receiver
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
161            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
161-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
162            android:directBootAware="false"
162-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
163            android:enabled="false"
163-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
164            android:exported="false" >
164-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
165            <intent-filter>
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
166                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
167            </intent-filter>
168        </receiver>
169        <receiver
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
170            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
171            android:directBootAware="false"
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
172            android:enabled="false"
172-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
173            android:exported="false" >
173-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
174            <intent-filter>
174-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
175                <action android:name="android.intent.action.BOOT_COMPLETED" />
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
176                <action android:name="android.intent.action.TIME_SET" />
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
177                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
178            </intent-filter>
179        </receiver>
180        <receiver
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
181            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
182            android:directBootAware="false"
182-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
183            android:enabled="@bool/enable_system_alarm_service_default"
183-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
184            android:exported="false" >
184-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
185            <intent-filter>
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
186                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
187            </intent-filter>
188        </receiver>
189        <receiver
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
190            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
191            android:directBootAware="false"
191-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
192            android:enabled="true"
192-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
193            android:exported="true"
193-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
194            android:permission="android.permission.DUMP" >
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
195            <intent-filter>
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
196                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
197            </intent-filter>
198        </receiver>
199
200        <service
200-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
201            android:name="androidx.room.MultiInstanceInvalidationService"
201-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
202            android:directBootAware="true"
202-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
203            android:exported="false" />
203-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
204
205        <activity
205-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
206            android:name="androidx.compose.ui.tooling.PreviewActivity"
206-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
207            android:exported="true" />
207-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
208        <activity
208-->[io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:15:9-19:45
209            android:name="io.github.g00fy2.quickie.QRScannerActivity"
209-->[io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:16:13-70
210            android:screenOrientation="behind"
210-->[io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:17:13-47
211            android:theme="@style/QuickieScannerActivity" />
211-->[io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:18:13-58
212
213        <service
213-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
214            android:name="androidx.camera.core.impl.MetadataHolderService"
214-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
215            android:enabled="false"
215-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
216            android:exported="false" >
216-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
217            <meta-data
217-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
218                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
218-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
219                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
219-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
220        </service>
221        <service
221-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
222            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
222-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
223            android:directBootAware="true"
223-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
224            android:exported="false" >
224-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
225            <meta-data
225-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
226                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
226-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
228            <meta-data
228-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
229                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
229-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
230                android:value="com.google.firebase.components.ComponentRegistrar" />
230-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
231            <meta-data
231-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
232                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
232-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
233                android:value="com.google.firebase.components.ComponentRegistrar" />
233-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
234        </service>
235
236        <provider
236-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
237            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
237-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
238            android:authorities="me.rerere.rikkahub.debug.mlkitinitprovider"
238-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
239            android:exported="false"
239-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
240            android:initOrder="99" />
240-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
241
242        <receiver
242-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
243            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
243-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
244            android:enabled="true"
244-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
245            android:exported="false" >
245-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
246        </receiver>
247
248        <service
248-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
249            android:name="com.google.android.gms.measurement.AppMeasurementService"
249-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
250            android:enabled="true"
250-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
251            android:exported="false" />
251-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
252        <service
252-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
253            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
253-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
254            android:enabled="true"
254-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
255            android:exported="false"
255-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
256            android:permission="android.permission.BIND_JOB_SERVICE" />
256-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
257
258        <activity
258-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
259            android:name="com.google.android.gms.common.api.GoogleApiActivity"
259-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
260            android:exported="false"
260-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
261            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
261-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
262
263        <service
263-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
264            android:name="com.google.firebase.components.ComponentDiscoveryService"
264-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
265            android:directBootAware="true"
265-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
266            android:exported="false" >
266-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
267            <meta-data
267-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
268                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
268-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
269                android:value="com.google.firebase.components.ComponentRegistrar" />
269-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
270            <meta-data
270-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:15:13-17:85
271                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
271-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:16:17-126
272                android:value="com.google.firebase.components.ComponentRegistrar" />
272-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:17:17-82
273            <meta-data
273-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:18:13-20:85
274                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
274-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:19:17-115
275                android:value="com.google.firebase.components.ComponentRegistrar" />
275-->[com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:20:17-82
276            <meta-data
276-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:29:13-31:85
277                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
277-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:30:17-117
278                android:value="com.google.firebase.components.ComponentRegistrar" />
278-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:31:17-82
279            <meta-data
279-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
280                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
280-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
281                android:value="com.google.firebase.components.ComponentRegistrar" />
281-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
282            <meta-data
282-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
283                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
283-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
284                android:value="com.google.firebase.components.ComponentRegistrar" />
284-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
285            <meta-data
285-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
286                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
286-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
287                android:value="com.google.firebase.components.ComponentRegistrar" />
287-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
288            <meta-data
288-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
289                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
289-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
290                android:value="com.google.firebase.components.ComponentRegistrar" />
290-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
291            <meta-data
291-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
292                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
292-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
293                android:value="com.google.firebase.components.ComponentRegistrar" />
293-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
294        </service>
295        <service
295-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:22:9-25:40
296            android:name="com.google.firebase.sessions.SessionLifecycleService"
296-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:23:13-80
297            android:enabled="true"
297-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:24:13-35
298            android:exported="false" />
298-->[com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:25:13-37
299
300        <provider
300-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
301            android:name="com.google.firebase.provider.FirebaseInitProvider"
301-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
302            android:authorities="me.rerere.rikkahub.debug.firebaseinitprovider"
302-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
303            android:directBootAware="true"
303-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
304            android:exported="false"
304-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
305            android:initOrder="100" />
305-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
306
307        <uses-library
307-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
308            android:name="android.ext.adservices"
308-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
309            android:required="false" />
309-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
310
311        <meta-data
311-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
312            android:name="com.google.android.gms.version"
312-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
313            android:value="@integer/google_play_services_version" />
313-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
314
315        <activity
315-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
316            android:name="androidx.activity.ComponentActivity"
316-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
317            android:exported="true"
317-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
318            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
318-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
319
320        <provider
320-->[com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:8:9-11:40
321            android:name="ru.noties.jlatexmath.JLatexMathInitProvider"
321-->[com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:9:13-71
322            android:authorities="me.rerere.rikkahub.debug.jlatexmathinitprovider"
322-->[com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:10:13-74
323            android:exported="false" />
323-->[com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:11:13-37
324        <provider
324-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:32:9-40:20
325            android:name="leakcanary.internal.LeakCanaryFileProvider"
325-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:33:13-70
326            android:authorities="com.squareup.leakcanary.fileprovider.me.rerere.rikkahub.debug"
326-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:34:13-88
327            android:exported="false"
327-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:35:13-37
328            android:grantUriPermissions="true" >
328-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:36:13-47
329            <meta-data
329-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:57:13-59:54
330                android:name="android.support.FILE_PROVIDER_PATHS"
330-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:58:17-67
331                android:resource="@xml/leak_canary_file_paths" />
331-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:59:17-51
332        </provider>
333
334        <activity
334-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:42:9-73:20
335            android:name="leakcanary.internal.activity.LeakActivity"
335-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:43:13-69
336            android:exported="true"
336-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:44:13-36
337            android:icon="@mipmap/leak_canary_icon"
337-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:45:13-52
338            android:label="@string/leak_canary_display_activity_label"
338-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:46:13-71
339            android:taskAffinity="com.squareup.leakcanary.me.rerere.rikkahub.debug"
339-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:47:13-76
340            android:theme="@style/leak_canary_LeakCanary.Base" >
340-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:48:13-63
341            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
341-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:49:13-72:29
341-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:49:28-81
342                <action android:name="android.intent.action.VIEW" />
342-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:50:17-69
342-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:50:25-66
343
344                <category android:name="android.intent.category.DEFAULT" />
344-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:52:17-76
344-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:52:27-73
345                <category android:name="android.intent.category.BROWSABLE" />
345-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:53:17-78
345-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:53:27-75
346
347                <data android:scheme="file" />
347-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
347-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:23-44
348                <data android:scheme="content" />
348-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
348-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:23-44
349                <data android:mimeType="*/*" />
349-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
350                <data android:host="*" />
350-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
351                <data android:pathPattern=".*\\.hprof" />
351-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
352                <data android:pathPattern=".*\\..*\\.hprof" />
352-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
353                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
353-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
354                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
354-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
355                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
355-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
356                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
356-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
357                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
357-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
358                <!--
359            Since hprof isn't a standard MIME type, we have to declare such patterns.
360            Most file providers will generate URIs including their own package name,
361            which contains `.` characters that must be explicitly escaped in pathPattern.
362            @see https://stackoverflow.com/a/31028507/703646
363                -->
364            </intent-filter>
365        </activity>
366
367        <activity-alias
367-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:75:9-92:26
368            android:name="leakcanary.internal.activity.LeakLauncherActivity"
368-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:76:13-77
369            android:banner="@drawable/leak_canary_tv_icon"
369-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:77:13-59
370            android:enabled="@bool/leak_canary_add_launcher_icon"
370-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:78:13-66
371            android:exported="true"
371-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:79:13-36
372            android:icon="@mipmap/leak_canary_icon"
372-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:80:13-52
373            android:label="@string/leak_canary_display_activity_label"
373-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:81:13-71
374            android:targetActivity="leakcanary.internal.activity.LeakActivity"
374-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:82:13-79
375            android:taskAffinity="com.squareup.leakcanary.me.rerere.rikkahub.debug"
375-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:83:13-76
376            android:theme="@style/leak_canary_LeakCanary.Base" >
376-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:84:13-63
377            <intent-filter>
377-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:85:13-91:29
378                <action android:name="android.intent.action.MAIN" />
378-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:36:17-69
378-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:36:25-66
379
380                <category android:name="android.intent.category.LAUNCHER" />
380-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:38:17-77
380-->D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:38:27-74
381                <!-- Android TV launcher intent -->
382                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
382-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:90:17-86
382-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:90:27-83
383            </intent-filter>
384        </activity-alias>
385
386        <activity
386-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:94:9-100:68
387            android:name="leakcanary.internal.RequestPermissionActivity"
387-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:95:13-73
388            android:excludeFromRecents="true"
388-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:96:13-46
389            android:icon="@mipmap/leak_canary_icon"
389-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:97:13-52
390            android:label="@string/leak_canary_storage_permission_activity_label"
390-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:98:13-82
391            android:taskAffinity="com.squareup.leakcanary.me.rerere.rikkahub.debug"
391-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:99:13-76
392            android:theme="@style/leak_canary_Theme.Transparent" />
392-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:100:13-65
393
394        <receiver android:name="leakcanary.internal.NotificationReceiver" />
394-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:102:9-77
394-->[com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:102:19-74
395
396        <provider
396-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:8:9-12:40
397            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
397-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:9:13-78
398            android:authorities="me.rerere.rikkahub.debug.leakcanary-installer"
398-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:10:13-72
399            android:enabled="@bool/leak_canary_watcher_auto_install"
399-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:11:13-69
400            android:exported="false" />
400-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:12:13-37
401        <provider
401-->[com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:8:9-12:40
402            android:name="leakcanary.internal.PlumberInstaller"
402-->[com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:9:13-64
403            android:authorities="me.rerere.rikkahub.debug.plumber-installer"
403-->[com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:10:13-69
404            android:enabled="@bool/leak_canary_plumber_auto_install"
404-->[com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:11:13-69
405            android:exported="false" />
405-->[com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:12:13-37
406
407        <service
407-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
408            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
408-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
409            android:exported="false" >
409-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
410            <meta-data
410-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
411                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
411-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
412                android:value="cct" />
412-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
413        </service>
414
415        <receiver
415-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
416            android:name="androidx.profileinstaller.ProfileInstallReceiver"
416-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
417            android:directBootAware="false"
417-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
418            android:enabled="true"
418-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
419            android:exported="true"
419-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
420            android:permission="android.permission.DUMP" >
420-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
421            <intent-filter>
421-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
422                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
422-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
422-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
423            </intent-filter>
424            <intent-filter>
424-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
425                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
425-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
425-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
426            </intent-filter>
427            <intent-filter>
427-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
428                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
428-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
428-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
429            </intent-filter>
430            <intent-filter>
430-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
431                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
431-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
431-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
432            </intent-filter>
433        </receiver>
434
435        <service
435-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
436            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
436-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
437            android:exported="false"
437-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
438            android:permission="android.permission.BIND_JOB_SERVICE" >
438-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
439        </service>
440
441        <receiver
441-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
442            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
442-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
443            android:exported="false" />
443-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
444    </application>
445
446</manifest>
