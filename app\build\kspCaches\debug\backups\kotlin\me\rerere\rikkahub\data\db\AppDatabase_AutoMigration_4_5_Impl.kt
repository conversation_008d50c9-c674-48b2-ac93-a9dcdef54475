package me.rerere.rikkahub.`data`.db

import androidx.room.migration.Migration
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL
import javax.`annotation`.processing.Generated
import kotlin.Suppress

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
internal class AppDatabase_AutoMigration_4_5_Impl : Migration {
  public constructor() : super(4, 5)

  public override fun migrate(connection: SQLiteConnection) {
    connection.execSQL("ALTER TABLE `ConversationEntity` ADD COLUMN `assistant_id` TEXT NOT NULL DEFAULT '0950e2dc-9bd5-4801-afa3-aa887aa36b4e'")
  }
}
