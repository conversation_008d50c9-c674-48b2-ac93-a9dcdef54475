package me.rerere.rikkahub.`data`.db

import androidx.room.InvalidationTracker
import androidx.room.RoomOpenDelegate
import androidx.room.migration.AutoMigrationSpec
import androidx.room.migration.Migration
import androidx.room.util.TableInfo
import androidx.room.util.TableInfo.Companion.read
import androidx.room.util.dropFtsSyncTriggers
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL
import javax.`annotation`.processing.Generated
import kotlin.Lazy
import kotlin.String
import kotlin.Suppress
import kotlin.collections.List
import kotlin.collections.Map
import kotlin.collections.MutableList
import kotlin.collections.MutableMap
import kotlin.collections.MutableSet
import kotlin.collections.Set
import kotlin.collections.mutableListOf
import kotlin.collections.mutableMapOf
import kotlin.collections.mutableSetOf
import kotlin.reflect.KClass
import me.rerere.rikkahub.`data`.db.dao.ConversationDAO
import me.rerere.rikkahub.`data`.db.dao.ConversationDAO_Impl
import me.rerere.rikkahub.`data`.db.dao.MemoryDAO
import me.rerere.rikkahub.`data`.db.dao.MemoryDAO_Impl

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class AppDatabase_Impl : AppDatabase() {
  private val _conversationDAO: Lazy<ConversationDAO> = lazy {
    ConversationDAO_Impl(this)
  }

  private val _memoryDAO: Lazy<MemoryDAO> = lazy {
    MemoryDAO_Impl(this)
  }

  protected override fun createOpenDelegate(): RoomOpenDelegate {
    val _openDelegate: RoomOpenDelegate = object : RoomOpenDelegate(5,
        "43ba212cc7af26d432b29031559d94c7", "85c30e791057a80ae4802ffa6b8b0638") {
      public override fun createAllTables(connection: SQLiteConnection) {
        connection.execSQL("CREATE TABLE IF NOT EXISTS `ConversationEntity` (`id` TEXT NOT NULL, `assistant_id` TEXT NOT NULL DEFAULT '0950e2dc-9bd5-4801-afa3-aa887aa36b4e', `title` TEXT NOT NULL, `messages` TEXT NOT NULL, `usage` TEXT, `create_at` INTEGER NOT NULL, `update_at` INTEGER NOT NULL, PRIMARY KEY(`id`))")
        connection.execSQL("CREATE TABLE IF NOT EXISTS `MemoryEntity` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `assistant_id` TEXT NOT NULL, `content` TEXT NOT NULL)")
        connection.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)")
        connection.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '43ba212cc7af26d432b29031559d94c7')")
      }

      public override fun dropAllTables(connection: SQLiteConnection) {
        connection.execSQL("DROP TABLE IF EXISTS `ConversationEntity`")
        connection.execSQL("DROP TABLE IF EXISTS `MemoryEntity`")
      }

      public override fun onCreate(connection: SQLiteConnection) {
      }

      public override fun onOpen(connection: SQLiteConnection) {
        internalInitInvalidationTracker(connection)
      }

      public override fun onPreMigrate(connection: SQLiteConnection) {
        dropFtsSyncTriggers(connection)
      }

      public override fun onPostMigrate(connection: SQLiteConnection) {
      }

      public override fun onValidateSchema(connection: SQLiteConnection):
          RoomOpenDelegate.ValidationResult {
        val _columnsConversationEntity: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsConversationEntity.put("id", TableInfo.Column("id", "TEXT", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsConversationEntity.put("assistant_id", TableInfo.Column("assistant_id", "TEXT",
            true, 0, "'0950e2dc-9bd5-4801-afa3-aa887aa36b4e'", TableInfo.CREATED_FROM_ENTITY))
        _columnsConversationEntity.put("title", TableInfo.Column("title", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsConversationEntity.put("messages", TableInfo.Column("messages", "TEXT", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsConversationEntity.put("usage", TableInfo.Column("usage", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsConversationEntity.put("create_at", TableInfo.Column("create_at", "INTEGER", true,
            0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsConversationEntity.put("update_at", TableInfo.Column("update_at", "INTEGER", true,
            0, null, TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysConversationEntity: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesConversationEntity: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoConversationEntity: TableInfo = TableInfo("ConversationEntity",
            _columnsConversationEntity, _foreignKeysConversationEntity, _indicesConversationEntity)
        val _existingConversationEntity: TableInfo = read(connection, "ConversationEntity")
        if (!_infoConversationEntity.equals(_existingConversationEntity)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |ConversationEntity(me.rerere.rikkahub.data.db.entity.ConversationEntity).
              | Expected:
              |""".trimMargin() + _infoConversationEntity + """
              |
              | Found:
              |""".trimMargin() + _existingConversationEntity)
        }
        val _columnsMemoryEntity: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsMemoryEntity.put("id", TableInfo.Column("id", "INTEGER", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsMemoryEntity.put("assistant_id", TableInfo.Column("assistant_id", "TEXT", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsMemoryEntity.put("content", TableInfo.Column("content", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysMemoryEntity: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesMemoryEntity: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoMemoryEntity: TableInfo = TableInfo("MemoryEntity", _columnsMemoryEntity,
            _foreignKeysMemoryEntity, _indicesMemoryEntity)
        val _existingMemoryEntity: TableInfo = read(connection, "MemoryEntity")
        if (!_infoMemoryEntity.equals(_existingMemoryEntity)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |MemoryEntity(me.rerere.rikkahub.data.db.entity.MemoryEntity).
              | Expected:
              |""".trimMargin() + _infoMemoryEntity + """
              |
              | Found:
              |""".trimMargin() + _existingMemoryEntity)
        }
        return RoomOpenDelegate.ValidationResult(true, null)
      }
    }
    return _openDelegate
  }

  protected override fun createInvalidationTracker(): InvalidationTracker {
    val _shadowTablesMap: MutableMap<String, String> = mutableMapOf()
    val _viewTables: MutableMap<String, Set<String>> = mutableMapOf()
    return InvalidationTracker(this, _shadowTablesMap, _viewTables, "ConversationEntity",
        "MemoryEntity")
  }

  public override fun clearAllTables() {
    super.performClear(false, "ConversationEntity", "MemoryEntity")
  }

  protected override fun getRequiredTypeConverterClasses(): Map<KClass<*>, List<KClass<*>>> {
    val _typeConvertersMap: MutableMap<KClass<*>, List<KClass<*>>> = mutableMapOf()
    _typeConvertersMap.put(ConversationDAO::class, ConversationDAO_Impl.getRequiredConverters())
    _typeConvertersMap.put(MemoryDAO::class, MemoryDAO_Impl.getRequiredConverters())
    return _typeConvertersMap
  }

  public override fun getRequiredAutoMigrationSpecClasses(): Set<KClass<out AutoMigrationSpec>> {
    val _autoMigrationSpecsSet: MutableSet<KClass<out AutoMigrationSpec>> = mutableSetOf()
    return _autoMigrationSpecsSet
  }

  public override
      fun createAutoMigrations(autoMigrationSpecs: Map<KClass<out AutoMigrationSpec>, AutoMigrationSpec>):
      List<Migration> {
    val _autoMigrations: MutableList<Migration> = mutableListOf()
    _autoMigrations.add(AppDatabase_AutoMigration_1_2_Impl())
    _autoMigrations.add(AppDatabase_AutoMigration_2_3_Impl())
    _autoMigrations.add(AppDatabase_AutoMigration_3_4_Impl())
    _autoMigrations.add(AppDatabase_AutoMigration_4_5_Impl())
    return _autoMigrations
  }

  public override fun conversationDao(): ConversationDAO = _conversationDAO.value

  public override fun memoryDao(): MemoryDAO = _memoryDAO.value
}
