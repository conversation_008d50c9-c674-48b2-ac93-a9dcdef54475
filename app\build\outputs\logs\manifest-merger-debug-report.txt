-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:52:9-60:20
	android:grantUriPermissions
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:56:13-47
	android:authorities
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:54:13-64
	android:exported
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:55:13-37
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:53:13-62
provider#androidx.startup.InitializationProvider
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:62:9-71:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9feb03463fc55eb65949cefbb07600c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9feb03463fc55eb65949cefbb07600c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:66:13-31
	android:authorities
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:64:13-68
	android:exported
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:65:13-37
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:63:13-67
manifest
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12
MERGED from [com.squareup.leakcanary:leakcanary-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a2c768a6f7edc96d85ec60d2c1b3ad23\transformed\leakcanary-android-2.14\AndroidManifest.xml:17:1-22:12
MERGED from [com.meticha:permissions_compose:0.0.1+4] C:\Users\<USER>\.gradle\caches\8.14\transforms\c49be1836815a99bcef5f5c5ff7ea4f2\transformed\permissions_compose-0.0.1+4\AndroidManifest.xml:2:1-7:12
MERGED from [com.jvziyaoyao.scale:image-viewer:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\c763968b850ffeebce9b924845366b87\transformed\image-viewer-1.1.0-alpha.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.jens-muenker:uCrop-n-Edit:4.1.0-non-native] C:\Users\<USER>\.gradle\caches\8.14\transforms\e744a31c82164d30026af487ba5e43bf\transformed\uCrop-n-Edit-4.1.0-non-native\AndroidManifest.xml:2:1-7:12
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eeee6154fcee33c4e7ed2c7bb3f9edd\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:2:1-9:12
MERGED from [io.insert-koin:koin-androidx-compose:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef0e5efa6ad5fc8809699e517aee3d92\transformed\koin-androidx-compose-4.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\d20c0ef6cd470db26bf3df8f3b966f0c\transformed\koin-android-4.0.4\AndroidManifest.xml:2:1-9:12
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:highlight] D:\FromTX3\MyCode\rikkahub\highlight\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9bc47a73ec95c06ac2c5ba3bd3dae81e\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e8357c48b32ec5bebd38b0e6f231e544\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd61bcf31320d6e00c553b77d3f9d1d3\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.4.0-alpha14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b48892203169544dad7e4ce4c2aaf9a0\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.jvziyaoyao.scale:zoomable-view:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b37c2cfd64546a337b2cd0d06241d2f\transformed\zoomable-view-1.1.0-alpha.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef8332ffe1e57a2eb93441c0ecf70800\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\44fb246a705bc9a3ff30369ef9e6dceb\transformed\room-ktx-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-paging-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a98b488c3f31eec9776a34969a89c3c8\transformed\room-paging-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.paging:paging-common-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\3e4b28f617d1dd52d7266c640084ad64\transformed\paging-common-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.paging:paging-runtime:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\d8fd8475067ee9b9c01ca1023c206aa7\transformed\paging-runtime-3.3.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.paging:paging-compose-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\2468d9b9dfddefaa8c65f5607707fcf9\transformed\paging-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b9676b1ec2d82f1cef53d671c6d0afde\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\9455810092f2ffbe42594d3df61d9066\transformed\reorderable-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.brdominguez:compose-sonner-android:0.3.10] C:\Users\<USER>\.gradle\caches\8.14\transforms\f475c70aa7c5f00c3a7e4565f4098ffe\transformed\sonner-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.composables:icons-lucide-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\46fad10297dd29df4dfe70653544e784\transformed\icons-lucide-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-compose-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\516808cb9326ff1ff21d8434091026d0\transformed\coil-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-compose-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9c3c09c19606e79bbfbb34a75f92e8e\transformed\coil-compose-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\bcd5b37d148734145e29078c82e4270c\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\698d94c31af48f8849c2d99537ea2d6e\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\519195194b816c1b9c42f7f6b27930e8\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\43b0f7839510fa1811f1bed92b138671\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\bd9e012c025b7c80daa2bbfa6a34ffed\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0e3115c16efab9568a5f0737dd9ff2e\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\8d7123aff87a2ed15b40f643111c50b0\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\f52079ae30d18fda33fef413a413ed2e\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b552fc4bffc7fc03a3e83d261975c13b\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\630bf066d71067618165a5f35bff2f25\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\e5979dfc39a8cd172314f2236b87f9eb\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a343b4853138a597870156ad621e8138\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:2:1-22:12
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8b170979be28cc1669b7f149806a80d\transformed\camera-video-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-view:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\49bc7e7978da1958f2a83d77a63d7fc8\transformed\camera-view-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b0313785e03e7a29f53fc70ffa0de55\transformed\camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\4d78ee200046e8ce702c1270225762bf\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f49d54fdb07570952b9a4cbeb34ba95d\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4400945f13d0d6ee004c1f772c22d20a\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7219de08abf79892e9add9bb5b00cd\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1c0646b106b31efcf60959b3bf9d7c8d\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\664d4a11ee170b0cabb7122cf881546f\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [io.coil-kt.coil3:coil-svg-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\75c8929ed3de2e9e66be9c814477b2ce\transformed\coil-svg-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8658d2dac68b61c127a0d4ece634cf4b\transformed\coil-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1bda48b7b4f2e12ca6e673fa7c13b2ed\transformed\coil-network-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\316c5671f7bd5c0be86ef5498abde56e\transformed\coil-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\15029d144dc7507aadd4bf956deac1ed\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\98eaa2bdae5c0aa44b683db3511b0efe\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\757e326fd7991b30514ac12e75a7701a\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b5f47b1480fad7829b6a842ab9f4474\transformed\transition-1.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0795945ec563958daa1b44111be66b9\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\63026ec565d9d329332353004448a230\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\dcc60ce582ed85f17a61b5aa261b2bd8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e3ce81d5f9340d75018b87dbd41d64d2\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\be0dd90539f51d08e115f434842498f4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\28be5a78d1acb1fa2374d56d19543227\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\77999506891db048630199676496c2b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e00b71a5d5243240b292e24f9ee86bb\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\dc8dc3f5e3df8e76f51a479586b291e1\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76871473690f2f76d302fddaa16cf0af\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ff462cec8a37cee390c9be7657e7510\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\bc099ba72b3425b6317cbcfc3a4edbcf\transformed\datastore-preferences-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\971eb48128cd3fd41d9adb7c329d596b\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\12f39f116c060b6a54f27dc6147eebde\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0d7f17b1d9009aeadfe3bdb5954b49b\transformed\fragment-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\837de6fdc560d6aadc786811faa0b7af\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a7da234436288e236d0dfde3e5f7489\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\87d48f0af44060d9325d5920b8689d81\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eef81e499b1e753f25470660f2f59eb\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0f7c165f4955c4e6c01cd915d5b7736\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0bb119e1f4d124813fd19380a09ae609\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9916ec00ef3fc230569c7b7ee2144060\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\25e74ff87714d1b5fdc1ec9186ded119\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\758bbca26c1aed656c9c0e67dbc89c5b\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1b93e391d3de6b53db264778dd74b5dc\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a44dbb2cd1205e808d258b16f821c830\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb3310559316df895653a4f3bc479329\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\32c39fb9949a630950d39a8cfd9474ce\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\ab5c6095209c09d587f1ceed2d98dbcb\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.37.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\1243c2de95ab8ad09c139221951e9dd3\transformed\accompanist-drawablepainter-0.37.3\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\7ecf809ffcccc89997986d0551f7888b\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f713984ee549868772e9f9224751136\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d94cc175fe4e4bb62a2b81878c87eb08\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f83d30e70b5d76f678fca35a62d3b673\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaed6e79bc8ea78655e3b270f6f95944\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b2702134ca325fb668e87ea48f084f19\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\11b578bb06fd9930c728ea2f6882b278\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f0b2afa2d8c80a042ac39ca36e413133\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fd3216e393eed8ca9e7373cf6b631201\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\16ecddf5f7e33c5426076aed915d023a\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4053c32c17d25b1b54c0a05e6c672cc8\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\90e4adc7762166e7e8b23d5514ca450b\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f70462a49f6e0d47edd30250f2cfe98a\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fdea05e7d84ae4f2959109973e03291c\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c48c77d7c93f15309a56cad3061739b6\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f8644304b7debfe3153a40a7a1073619\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\694348845b7cfbd173a41bfd79535df9\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ec01753cc085eccabdf318240ae84988\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3150978528ae29009dee5d0c59b988a4\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\90cd1ea8dda767b099c8fa4f8d524e1b\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\360a543f313302ee6c15a3058eaa7646\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b08386bd93a874d2c29da6c8c378f54a\transformed\graphics-shapes-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\52a953be5e7f2d17335f334d7e0a50a6\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\729b5d4a7a1dc5ee987f923848e9dc16\transformed\core-bundle-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d1b5a918adecb201351729864883f56c\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:2:1-14:12
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-greek:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1d2a487f579ab18a55eda34e3782452d\transformed\jlatexmath-font-greek-1.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-cyrillic:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\f15665b364552c05663e60de304ad4b0\transformed\jlatexmath-font-cyrillic-1.2\AndroidManifest.xml:2:1-7:12
MERGED from [:rag] D:\FromTX3\MyCode\rikkahub\rag\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:17:1-105:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:2:1-15:12
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e689217588655858bf3b8be6365d689f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c566d7d4cbd080caa2b7f189ce421c69\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb0ea3829ad156afd6fc7e6f42c406ea\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\42c8ab9c742b4de261245c206698909e\transformed\leakcanary-object-watcher-android-androidx-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b9ef0385caf03b0f47982c619acf3044\transformed\leakcanary-object-watcher-android-support-fragments-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\1869850e530ba93679cdf61c8d18dcd1\transformed\leakcanary-object-watcher-android-core-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:plumber-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\38b84be4f21957a8d367b2654362e159\transformed\plumber-android-core-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\177564c4eeebab765dd1d6f5666470b3\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9feb03463fc55eb65949cefbb07600c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\debe6f85312831958693c6f898151843\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0bad87cd33b5b10ad0542c352e36bd0\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.databinding:viewbinding:8.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a299c954038cec99e94136527aa06bbc\transformed\viewbinding-8.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\86fd9622d90eca91baacea3e50e15474\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ee75c174b778bb5d25009e69a01ca1f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fa9ee6477f3c074042e6fb017ffe939d\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8918adcdbee2f70a4e5966c1782f7afd\transformed\transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\63b5938529bed9bae81fa13c43132e7a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\49b93ca932932cc2e76c88b37441c1a6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\80aacdbbe32dccfafe43a372ff627c13\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bd87c342e632dd9f6ba17b5be06f7a8e\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f9325d818243d8f161ef5f001de6a1\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f74b9894d28758015877ef7ccfe9961\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0cb01675e4da7f76a6f0896f00b8244d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d56e7eaa1092ebc68b47131ae2206385\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b77894397f1bc8c9b09e4e50a0915acb\transformed\exifinterface-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\344a9fcd7e7303952cbd6e63cc836113\transformed\leakcanary-android-utils-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\70c5c31b6a18302d7c109b5e9cd6cfd6\transformed\curtains-1.2.4\AndroidManifest.xml:2:1-9:12
MERGED from [io.github.oshai:kotlin-logging-android-debug:7.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\70cd9df3ddf98570030fae85218e9319\transformed\kotlin-logging-debug\AndroidManifest.xml:2:1-7:12
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\04146bda52b30f8dd7c226023c3a9da8\transformed\wrapper-android-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.objectbox:objectbox-android:4.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f3bb822a67dd4bebd6090ce8deba7e8\transformed\objectbox-android-4.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef4907c15ce1140668bf848d199c39a8\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\de590e017df6226c8f4976cad468047b\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.CAMERA
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:6:5-65
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:12:5-65
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:12:5-65
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:7:5-77
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:29:5-77
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:29:5-77
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:7:22-74
uses-feature#android.hardware.camera
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:9:5-11:36
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:8:5-10:36
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:8:5-10:36
	android:required
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:11:9-33
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:10:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:12:5-14:36
	android:required
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:14:9-33
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:13:9-57
application
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:16:5-72:19
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:16:5-72:19
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eeee6154fcee33c4e7ed2c7bb3f9edd\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eeee6154fcee33c4e7ed2c7bb3f9edd\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\d20c0ef6cd470db26bf3df8f3b966f0c\transformed\koin-android-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\d20c0ef6cd470db26bf3df8f3b966f0c\transformed\koin-android-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:14:5-20:19
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:14:5-20:19
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\4d78ee200046e8ce702c1270225762bf\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\4d78ee200046e8ce702c1270225762bf\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7219de08abf79892e9add9bb5b00cd\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7219de08abf79892e9add9bb5b00cd\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\98eaa2bdae5c0aa44b683db3511b0efe\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\98eaa2bdae5c0aa44b683db3511b0efe\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0795945ec563958daa1b44111be66b9\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0795945ec563958daa1b44111be66b9\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\63026ec565d9d329332353004448a230\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\63026ec565d9d329332353004448a230\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e00b71a5d5243240b292e24f9ee86bb\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e00b71a5d5243240b292e24f9ee86bb\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eef81e499b1e753f25470660f2f59eb\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eef81e499b1e753f25470660f2f59eb\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d94cc175fe4e4bb62a2b81878c87eb08\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d94cc175fe4e4bb62a2b81878c87eb08\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f83d30e70b5d76f678fca35a62d3b673\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f83d30e70b5d76f678fca35a62d3b673\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaed6e79bc8ea78655e3b270f6f95944\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaed6e79bc8ea78655e3b270f6f95944\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:7:5-12:19
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:7:5-12:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9feb03463fc55eb65949cefbb07600c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9feb03463fc55eb65949cefbb07600c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ee75c174b778bb5d25009e69a01ca1f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ee75c174b778bb5d25009e69a01ca1f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\de590e017df6226c8f4976cad468047b\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\de590e017df6226c8f4976cad468047b\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	android:appCategory
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:19:9-43
	android:icon
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:23:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:25:9-35
	android:label
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:24:9-41
	android:fullBackupContent
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:22:9-54
	tools:targetApi
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:28:9-29
	android:allowBackup
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:18:9-35
	android:theme
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:26:9-46
	android:enableOnBackInvokedCallback
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:21:9-51
	android:dataExtractionRules
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:20:9-65
	android:usesCleartextTraffic
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:27:9-44
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:17:9-36
activity#me.rerere.rikkahub.RouteActivity
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:29:9-40:20
	android:windowSoftInputMode
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:34:13-55
	android:exported
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:32:13-36
	android:configChanges
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:31:13-74
	android:theme
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:33:13-50
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:30:13-42
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:35:13-39:29
action#android.intent.action.MAIN
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:36:17-69
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:36:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:38:17-77
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:38:27-74
activity#com.yalantis.ucrop.UCropActivity
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:42:9-44:72
	android:theme
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:44:13-69
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:43:13-60
service#me.rerere.rikkahub.services.ChatService
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:46:9-50:15
	android:enabled
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:48:13-35
	android:foregroundServiceType
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:49:13-60
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:47:13-49
queries
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:74:5-78:15
intent#action:name:android.intent.action.TTS_SERVICE
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:75:9-77:18
action#android.intent.action.TTS_SERVICE
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:76:13-72
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:76:21-69
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:57:13-59:54
	android:resource
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:59:17-51
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:58:17-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:67:13-70:39
REJECTED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:70:17-36
	android:value
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:69:17-49
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:68:17-68
uses-sdk
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
MERGED from [com.squareup.leakcanary:leakcanary-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a2c768a6f7edc96d85ec60d2c1b3ad23\transformed\leakcanary-android-2.14\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a2c768a6f7edc96d85ec60d2c1b3ad23\transformed\leakcanary-android-2.14\AndroidManifest.xml:20:5-44
MERGED from [com.meticha:permissions_compose:0.0.1+4] C:\Users\<USER>\.gradle\caches\8.14\transforms\c49be1836815a99bcef5f5c5ff7ea4f2\transformed\permissions_compose-0.0.1+4\AndroidManifest.xml:5:5-44
MERGED from [com.meticha:permissions_compose:0.0.1+4] C:\Users\<USER>\.gradle\caches\8.14\transforms\c49be1836815a99bcef5f5c5ff7ea4f2\transformed\permissions_compose-0.0.1+4\AndroidManifest.xml:5:5-44
MERGED from [com.jvziyaoyao.scale:image-viewer:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\c763968b850ffeebce9b924845366b87\transformed\image-viewer-1.1.0-alpha.7\AndroidManifest.xml:5:5-44
MERGED from [com.jvziyaoyao.scale:image-viewer:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\c763968b850ffeebce9b924845366b87\transformed\image-viewer-1.1.0-alpha.7\AndroidManifest.xml:5:5-44
MERGED from [com.github.jens-muenker:uCrop-n-Edit:4.1.0-non-native] C:\Users\<USER>\.gradle\caches\8.14\transforms\e744a31c82164d30026af487ba5e43bf\transformed\uCrop-n-Edit-4.1.0-non-native\AndroidManifest.xml:5:5-44
MERGED from [com.github.jens-muenker:uCrop-n-Edit:4.1.0-non-native] C:\Users\<USER>\.gradle\caches\8.14\transforms\e744a31c82164d30026af487ba5e43bf\transformed\uCrop-n-Edit-4.1.0-non-native\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eeee6154fcee33c4e7ed2c7bb3f9edd\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eeee6154fcee33c4e7ed2c7bb3f9edd\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-compose:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef0e5efa6ad5fc8809699e517aee3d92\transformed\koin-androidx-compose-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-compose:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef0e5efa6ad5fc8809699e517aee3d92\transformed\koin-androidx-compose-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\d20c0ef6cd470db26bf3df8f3b966f0c\transformed\koin-android-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\d20c0ef6cd470db26bf3df8f3b966f0c\transformed\koin-android-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:highlight] D:\FromTX3\MyCode\rikkahub\highlight\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:highlight] D:\FromTX3\MyCode\rikkahub\highlight\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9bc47a73ec95c06ac2c5ba3bd3dae81e\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9bc47a73ec95c06ac2c5ba3bd3dae81e\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e8357c48b32ec5bebd38b0e6f231e544\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e8357c48b32ec5bebd38b0e6f231e544\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd61bcf31320d6e00c553b77d3f9d1d3\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd61bcf31320d6e00c553b77d3f9d1d3\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.4.0-alpha14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b48892203169544dad7e4ce4c2aaf9a0\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.4.0-alpha14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b48892203169544dad7e4ce4c2aaf9a0\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [com.jvziyaoyao.scale:zoomable-view:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b37c2cfd64546a337b2cd0d06241d2f\transformed\zoomable-view-1.1.0-alpha.7\AndroidManifest.xml:5:5-44
MERGED from [com.jvziyaoyao.scale:zoomable-view:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b37c2cfd64546a337b2cd0d06241d2f\transformed\zoomable-view-1.1.0-alpha.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef8332ffe1e57a2eb93441c0ecf70800\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef8332ffe1e57a2eb93441c0ecf70800\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\44fb246a705bc9a3ff30369ef9e6dceb\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\44fb246a705bc9a3ff30369ef9e6dceb\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-paging-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a98b488c3f31eec9776a34969a89c3c8\transformed\room-paging-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-paging-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a98b488c3f31eec9776a34969a89c3c8\transformed\room-paging-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.paging:paging-common-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\3e4b28f617d1dd52d7266c640084ad64\transformed\paging-common-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-common-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\3e4b28f617d1dd52d7266c640084ad64\transformed\paging-common-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-runtime:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\d8fd8475067ee9b9c01ca1023c206aa7\transformed\paging-runtime-3.3.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-runtime:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\d8fd8475067ee9b9c01ca1023c206aa7\transformed\paging-runtime-3.3.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-compose-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\2468d9b9dfddefaa8c65f5607707fcf9\transformed\paging-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-compose-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\2468d9b9dfddefaa8c65f5607707fcf9\transformed\paging-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b9676b1ec2d82f1cef53d671c6d0afde\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b9676b1ec2d82f1cef53d671c6d0afde\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\9455810092f2ffbe42594d3df61d9066\transformed\reorderable-debug\AndroidManifest.xml:5:5-44
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\9455810092f2ffbe42594d3df61d9066\transformed\reorderable-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.brdominguez:compose-sonner-android:0.3.10] C:\Users\<USER>\.gradle\caches\8.14\transforms\f475c70aa7c5f00c3a7e4565f4098ffe\transformed\sonner-release\AndroidManifest.xml:5:5-43
MERGED from [io.github.brdominguez:compose-sonner-android:0.3.10] C:\Users\<USER>\.gradle\caches\8.14\transforms\f475c70aa7c5f00c3a7e4565f4098ffe\transformed\sonner-release\AndroidManifest.xml:5:5-43
MERGED from [com.composables:icons-lucide-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\46fad10297dd29df4dfe70653544e784\transformed\icons-lucide-debug\AndroidManifest.xml:5:5-44
MERGED from [com.composables:icons-lucide-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\46fad10297dd29df4dfe70653544e784\transformed\icons-lucide-debug\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\516808cb9326ff1ff21d8434091026d0\transformed\coil-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\516808cb9326ff1ff21d8434091026d0\transformed\coil-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9c3c09c19606e79bbfbb34a75f92e8e\transformed\coil-compose-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9c3c09c19606e79bbfbb34a75f92e8e\transformed\coil-compose-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\bcd5b37d148734145e29078c82e4270c\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\bcd5b37d148734145e29078c82e4270c\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\698d94c31af48f8849c2d99537ea2d6e\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\698d94c31af48f8849c2d99537ea2d6e\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\519195194b816c1b9c42f7f6b27930e8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\519195194b816c1b9c42f7f6b27930e8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\43b0f7839510fa1811f1bed92b138671\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\43b0f7839510fa1811f1bed92b138671\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\bd9e012c025b7c80daa2bbfa6a34ffed\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\bd9e012c025b7c80daa2bbfa6a34ffed\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0e3115c16efab9568a5f0737dd9ff2e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0e3115c16efab9568a5f0737dd9ff2e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\8d7123aff87a2ed15b40f643111c50b0\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\8d7123aff87a2ed15b40f643111c50b0\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\f52079ae30d18fda33fef413a413ed2e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\f52079ae30d18fda33fef413a413ed2e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b552fc4bffc7fc03a3e83d261975c13b\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b552fc4bffc7fc03a3e83d261975c13b\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\630bf066d71067618165a5f35bff2f25\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\630bf066d71067618165a5f35bff2f25\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\e5979dfc39a8cd172314f2236b87f9eb\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\e5979dfc39a8cd172314f2236b87f9eb\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a343b4853138a597870156ad621e8138\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a343b4853138a597870156ad621e8138\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:6:5-44
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8b170979be28cc1669b7f149806a80d\transformed\camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8b170979be28cc1669b7f149806a80d\transformed\camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\49bc7e7978da1958f2a83d77a63d7fc8\transformed\camera-view-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\49bc7e7978da1958f2a83d77a63d7fc8\transformed\camera-view-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b0313785e03e7a29f53fc70ffa0de55\transformed\camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b0313785e03e7a29f53fc70ffa0de55\transformed\camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\4d78ee200046e8ce702c1270225762bf\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\4d78ee200046e8ce702c1270225762bf\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f49d54fdb07570952b9a4cbeb34ba95d\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f49d54fdb07570952b9a4cbeb34ba95d\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4400945f13d0d6ee004c1f772c22d20a\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4400945f13d0d6ee004c1f772c22d20a\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7219de08abf79892e9add9bb5b00cd\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7219de08abf79892e9add9bb5b00cd\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1c0646b106b31efcf60959b3bf9d7c8d\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1c0646b106b31efcf60959b3bf9d7c8d\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\664d4a11ee170b0cabb7122cf881546f\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\664d4a11ee170b0cabb7122cf881546f\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [io.coil-kt.coil3:coil-svg-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\75c8929ed3de2e9e66be9c814477b2ce\transformed\coil-svg-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-svg-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\75c8929ed3de2e9e66be9c814477b2ce\transformed\coil-svg-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8658d2dac68b61c127a0d4ece634cf4b\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8658d2dac68b61c127a0d4ece634cf4b\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1bda48b7b4f2e12ca6e673fa7c13b2ed\transformed\coil-network-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1bda48b7b4f2e12ca6e673fa7c13b2ed\transformed\coil-network-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\316c5671f7bd5c0be86ef5498abde56e\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\316c5671f7bd5c0be86ef5498abde56e\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\15029d144dc7507aadd4bf956deac1ed\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\15029d144dc7507aadd4bf956deac1ed\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\98eaa2bdae5c0aa44b683db3511b0efe\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\98eaa2bdae5c0aa44b683db3511b0efe\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\757e326fd7991b30514ac12e75a7701a\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\757e326fd7991b30514ac12e75a7701a\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b5f47b1480fad7829b6a842ab9f4474\transformed\transition-1.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b5f47b1480fad7829b6a842ab9f4474\transformed\transition-1.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0795945ec563958daa1b44111be66b9\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0795945ec563958daa1b44111be66b9\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\63026ec565d9d329332353004448a230\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\63026ec565d9d329332353004448a230\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\dcc60ce582ed85f17a61b5aa261b2bd8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\dcc60ce582ed85f17a61b5aa261b2bd8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e3ce81d5f9340d75018b87dbd41d64d2\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e3ce81d5f9340d75018b87dbd41d64d2\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\be0dd90539f51d08e115f434842498f4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\be0dd90539f51d08e115f434842498f4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\28be5a78d1acb1fa2374d56d19543227\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\28be5a78d1acb1fa2374d56d19543227\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\77999506891db048630199676496c2b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\77999506891db048630199676496c2b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e00b71a5d5243240b292e24f9ee86bb\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e00b71a5d5243240b292e24f9ee86bb\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\dc8dc3f5e3df8e76f51a479586b291e1\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\dc8dc3f5e3df8e76f51a479586b291e1\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76871473690f2f76d302fddaa16cf0af\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76871473690f2f76d302fddaa16cf0af\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ff462cec8a37cee390c9be7657e7510\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ff462cec8a37cee390c9be7657e7510\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\bc099ba72b3425b6317cbcfc3a4edbcf\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\bc099ba72b3425b6317cbcfc3a4edbcf\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\971eb48128cd3fd41d9adb7c329d596b\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\971eb48128cd3fd41d9adb7c329d596b\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\12f39f116c060b6a54f27dc6147eebde\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\12f39f116c060b6a54f27dc6147eebde\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0d7f17b1d9009aeadfe3bdb5954b49b\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0d7f17b1d9009aeadfe3bdb5954b49b\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\837de6fdc560d6aadc786811faa0b7af\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\837de6fdc560d6aadc786811faa0b7af\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a7da234436288e236d0dfde3e5f7489\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a7da234436288e236d0dfde3e5f7489\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\87d48f0af44060d9325d5920b8689d81\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\87d48f0af44060d9325d5920b8689d81\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eef81e499b1e753f25470660f2f59eb\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1eef81e499b1e753f25470660f2f59eb\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0f7c165f4955c4e6c01cd915d5b7736\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0f7c165f4955c4e6c01cd915d5b7736\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0bb119e1f4d124813fd19380a09ae609\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0bb119e1f4d124813fd19380a09ae609\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9916ec00ef3fc230569c7b7ee2144060\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9916ec00ef3fc230569c7b7ee2144060\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\25e74ff87714d1b5fdc1ec9186ded119\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\25e74ff87714d1b5fdc1ec9186ded119\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\758bbca26c1aed656c9c0e67dbc89c5b\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\758bbca26c1aed656c9c0e67dbc89c5b\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1b93e391d3de6b53db264778dd74b5dc\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1b93e391d3de6b53db264778dd74b5dc\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a44dbb2cd1205e808d258b16f821c830\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a44dbb2cd1205e808d258b16f821c830\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb3310559316df895653a4f3bc479329\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb3310559316df895653a4f3bc479329\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\32c39fb9949a630950d39a8cfd9474ce\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\32c39fb9949a630950d39a8cfd9474ce\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\ab5c6095209c09d587f1ceed2d98dbcb\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\ab5c6095209c09d587f1ceed2d98dbcb\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.37.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\1243c2de95ab8ad09c139221951e9dd3\transformed\accompanist-drawablepainter-0.37.3\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.37.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\1243c2de95ab8ad09c139221951e9dd3\transformed\accompanist-drawablepainter-0.37.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\7ecf809ffcccc89997986d0551f7888b\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\7ecf809ffcccc89997986d0551f7888b\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f713984ee549868772e9f9224751136\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f713984ee549868772e9f9224751136\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d94cc175fe4e4bb62a2b81878c87eb08\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d94cc175fe4e4bb62a2b81878c87eb08\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f83d30e70b5d76f678fca35a62d3b673\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f83d30e70b5d76f678fca35a62d3b673\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaed6e79bc8ea78655e3b270f6f95944\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaed6e79bc8ea78655e3b270f6f95944\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b2702134ca325fb668e87ea48f084f19\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b2702134ca325fb668e87ea48f084f19\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\11b578bb06fd9930c728ea2f6882b278\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\11b578bb06fd9930c728ea2f6882b278\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f0b2afa2d8c80a042ac39ca36e413133\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f0b2afa2d8c80a042ac39ca36e413133\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fd3216e393eed8ca9e7373cf6b631201\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fd3216e393eed8ca9e7373cf6b631201\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\16ecddf5f7e33c5426076aed915d023a\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\16ecddf5f7e33c5426076aed915d023a\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4053c32c17d25b1b54c0a05e6c672cc8\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4053c32c17d25b1b54c0a05e6c672cc8\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\90e4adc7762166e7e8b23d5514ca450b\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\90e4adc7762166e7e8b23d5514ca450b\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f70462a49f6e0d47edd30250f2cfe98a\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f70462a49f6e0d47edd30250f2cfe98a\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fdea05e7d84ae4f2959109973e03291c\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fdea05e7d84ae4f2959109973e03291c\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c48c77d7c93f15309a56cad3061739b6\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c48c77d7c93f15309a56cad3061739b6\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f8644304b7debfe3153a40a7a1073619\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f8644304b7debfe3153a40a7a1073619\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\694348845b7cfbd173a41bfd79535df9\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\694348845b7cfbd173a41bfd79535df9\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ec01753cc085eccabdf318240ae84988\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ec01753cc085eccabdf318240ae84988\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3150978528ae29009dee5d0c59b988a4\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3150978528ae29009dee5d0c59b988a4\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\90cd1ea8dda767b099c8fa4f8d524e1b\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\90cd1ea8dda767b099c8fa4f8d524e1b\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\360a543f313302ee6c15a3058eaa7646\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\360a543f313302ee6c15a3058eaa7646\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b08386bd93a874d2c29da6c8c378f54a\transformed\graphics-shapes-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b08386bd93a874d2c29da6c8c378f54a\transformed\graphics-shapes-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\52a953be5e7f2d17335f334d7e0a50a6\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\52a953be5e7f2d17335f334d7e0a50a6\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\729b5d4a7a1dc5ee987f923848e9dc16\transformed\core-bundle-debug\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\729b5d4a7a1dc5ee987f923848e9dc16\transformed\core-bundle-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d1b5a918adecb201351729864883f56c\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d1b5a918adecb201351729864883f56c\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-greek:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1d2a487f579ab18a55eda34e3782452d\transformed\jlatexmath-font-greek-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-greek:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1d2a487f579ab18a55eda34e3782452d\transformed\jlatexmath-font-greek-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-cyrillic:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\f15665b364552c05663e60de304ad4b0\transformed\jlatexmath-font-cyrillic-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-cyrillic:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\f15665b364552c05663e60de304ad4b0\transformed\jlatexmath-font-cyrillic-1.2\AndroidManifest.xml:5:5-44
MERGED from [:rag] D:\FromTX3\MyCode\rikkahub\rag\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rag] D:\FromTX3\MyCode\rikkahub\rag\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e689217588655858bf3b8be6365d689f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e689217588655858bf3b8be6365d689f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c566d7d4cbd080caa2b7f189ce421c69\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c566d7d4cbd080caa2b7f189ce421c69\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb0ea3829ad156afd6fc7e6f42c406ea\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb0ea3829ad156afd6fc7e6f42c406ea\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\42c8ab9c742b4de261245c206698909e\transformed\leakcanary-object-watcher-android-androidx-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\42c8ab9c742b4de261245c206698909e\transformed\leakcanary-object-watcher-android-androidx-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b9ef0385caf03b0f47982c619acf3044\transformed\leakcanary-object-watcher-android-support-fragments-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b9ef0385caf03b0f47982c619acf3044\transformed\leakcanary-object-watcher-android-support-fragments-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\1869850e530ba93679cdf61c8d18dcd1\transformed\leakcanary-object-watcher-android-core-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\1869850e530ba93679cdf61c8d18dcd1\transformed\leakcanary-object-watcher-android-core-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\38b84be4f21957a8d367b2654362e159\transformed\plumber-android-core-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\38b84be4f21957a8d367b2654362e159\transformed\plumber-android-core-2.14\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\177564c4eeebab765dd1d6f5666470b3\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\177564c4eeebab765dd1d6f5666470b3\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9feb03463fc55eb65949cefbb07600c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9feb03463fc55eb65949cefbb07600c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\debe6f85312831958693c6f898151843\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\debe6f85312831958693c6f898151843\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0bad87cd33b5b10ad0542c352e36bd0\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0bad87cd33b5b10ad0542c352e36bd0\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.databinding:viewbinding:8.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a299c954038cec99e94136527aa06bbc\transformed\viewbinding-8.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a299c954038cec99e94136527aa06bbc\transformed\viewbinding-8.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\86fd9622d90eca91baacea3e50e15474\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\86fd9622d90eca91baacea3e50e15474\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ee75c174b778bb5d25009e69a01ca1f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ee75c174b778bb5d25009e69a01ca1f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fa9ee6477f3c074042e6fb017ffe939d\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fa9ee6477f3c074042e6fb017ffe939d\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8918adcdbee2f70a4e5966c1782f7afd\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8918adcdbee2f70a4e5966c1782f7afd\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\63b5938529bed9bae81fa13c43132e7a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\63b5938529bed9bae81fa13c43132e7a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\49b93ca932932cc2e76c88b37441c1a6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\49b93ca932932cc2e76c88b37441c1a6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\80aacdbbe32dccfafe43a372ff627c13\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\80aacdbbe32dccfafe43a372ff627c13\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bd87c342e632dd9f6ba17b5be06f7a8e\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bd87c342e632dd9f6ba17b5be06f7a8e\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f9325d818243d8f161ef5f001de6a1\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f9325d818243d8f161ef5f001de6a1\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f74b9894d28758015877ef7ccfe9961\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f74b9894d28758015877ef7ccfe9961\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0cb01675e4da7f76a6f0896f00b8244d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0cb01675e4da7f76a6f0896f00b8244d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d56e7eaa1092ebc68b47131ae2206385\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d56e7eaa1092ebc68b47131ae2206385\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b77894397f1bc8c9b09e4e50a0915acb\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b77894397f1bc8c9b09e4e50a0915acb\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\344a9fcd7e7303952cbd6e63cc836113\transformed\leakcanary-android-utils-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\344a9fcd7e7303952cbd6e63cc836113\transformed\leakcanary-android-utils-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\70c5c31b6a18302d7c109b5e9cd6cfd6\transformed\curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\70c5c31b6a18302d7c109b5e9cd6cfd6\transformed\curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.oshai:kotlin-logging-android-debug:7.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\70cd9df3ddf98570030fae85218e9319\transformed\kotlin-logging-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.oshai:kotlin-logging-android-debug:7.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\70cd9df3ddf98570030fae85218e9319\transformed\kotlin-logging-debug\AndroidManifest.xml:5:5-44
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\04146bda52b30f8dd7c226023c3a9da8\transformed\wrapper-android-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\04146bda52b30f8dd7c226023c3a9da8\transformed\wrapper-android-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [io.objectbox:objectbox-android:4.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f3bb822a67dd4bebd6090ce8deba7e8\transformed\objectbox-android-4.3.0\AndroidManifest.xml:6:5-44
MERGED from [io.objectbox:objectbox-android:4.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f3bb822a67dd4bebd6090ce8deba7e8\transformed\objectbox-android-4.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef4907c15ce1140668bf848d199c39a8\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef4907c15ce1140668bf848d199c39a8\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\de590e017df6226c8f4976cad468047b\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\de590e017df6226c8f4976cad468047b\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a6ea53626928c16932c24a25c5a0066\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b24b1e5447f0dbc8bb4ce6c88cbbab17\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
IMPLIED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12 reason: com.dokar.sonner.core has a targetSdkVersion < 4
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:26:5-81
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:26:5-81
uses-permission#android.permission.READ_PHONE_STATE
IMPLIED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12 reason: com.dokar.sonner.core has a targetSdkVersion < 4
uses-permission#android.permission.READ_EXTERNAL_STORAGE
IMPLIED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12 reason: com.dokar.sonner.core requested WRITE_EXTERNAL_STORAGE
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:25:5-80
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:25:5-80
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f654f0af7eba0f8b81ba504b49382bd\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#io.github.g00fy2.quickie.QRScannerActivity
ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:15:9-19:45
	android:screenOrientation
		ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:17:13-47
	tools:ignore
		ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:19:13-42
	android:theme
		ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:18:13-58
	android:name
		ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c0ee106e096d6fe0bd1735cc2a0a4c89\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:16:13-70
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\4d78ee200046e8ce702c1270225762bf\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\4d78ee200046e8ce702c1270225762bf\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\81e61e5f94835ee72da2a3cff911528a\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8df66e786919d643debfcae2f67e54f9\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5887d9a3ff0a5e5cf696e73ac688601b\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7f08f62191a8be669074289c459478bc\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b89696addffec47abe2dff822968ea7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f83d30e70b5d76f678fca35a62d3b673\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f83d30e70b5d76f678fca35a62d3b673\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e5d7b6f42b7ce0247ace91f978302b0\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:22-76
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9436b4ebc37cac3d97710224e74d8985\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b02c92ae969eb958425d14d38277390c\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\34b51f10ec62f52b45e16207ca8f794f\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fc7aa62dbb1891fbf85b860b020bdca\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:19:17-115
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\4b43461a7db3d97b44dc98eaadb76ff1\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79cf5435f3ee7ccb09adafab10634d6f\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\79e8736969d30fc0c22ae94a6c402d4a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5df1cc15270ddb2bc2e8c06a51e355bf\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1236221dabb70d81941dc4d57fc57e9b\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4a6f62de3decd067aa1ac2af94e24a9\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\d96aaad66a32e7a679b217f072a560c2\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\96abb1e1502d74c75d73c35e14f81e22\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#me.rerere.rikkahub.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#me.rerere.rikkahub.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f58a4462c0f661bc016516b04b05ffbf\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\aca322fdaaec900c12742297035598aa\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
provider#ru.noties.jlatexmath.JLatexMathInitProvider
ADDED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:8:9-11:40
	android:authorities
		ADDED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:10:13-74
	android:exported
		ADDED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\50cd21f4ca7714cb43f14b7ad37b6e9d\transformed\jlatexmath-1.2\AndroidManifest.xml:9:13-71
provider#leakcanary.internal.LeakCanaryFileProvider
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:32:9-40:20
	android:grantUriPermissions
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:36:13-47
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:34:13-88
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:33:13-70
activity#leakcanary.internal.activity.LeakActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:42:9-73:20
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:46:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:44:13-36
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:45:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:48:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:47:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:43:13-69
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:mimeType:*/*+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\.hprof+data:pathPattern:.*\\.hprof+data:scheme:content+data:scheme:file
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:49:13-72:29
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:49:28-81
action#android.intent.action.VIEW
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:50:17-69
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:50:25-66
category#android.intent.category.DEFAULT
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:52:17-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:52:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:53:17-78
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:53:27-75
data
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
	android:scheme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:23-44
activity-alias#leakcanary.internal.activity.LeakLauncherActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:75:9-92:26
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:78:13-66
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:81:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:79:13-36
	android:targetActivity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:82:13-79
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:80:13-52
	android:banner
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:77:13-59
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:84:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:83:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:76:13-77
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:85:13-91:29
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:90:17-86
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:90:27-83
activity#leakcanary.internal.RequestPermissionActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:94:9-100:68
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:98:13-82
	android:excludeFromRecents
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:96:13-46
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:97:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:100:13-65
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:99:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:95:13-73
receiver#leakcanary.internal.NotificationReceiver
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:102:9-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a0bc8b68683f96058cd04f29e7bad1e4\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:102:19-74
provider#leakcanary.internal.MainProcessAppWatcherInstaller
ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\05b55c1fa2199e4e83223411d2b088fe\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:9:13-78
provider#leakcanary.internal.PlumberInstaller
ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:10:13-69
	android:exported
		ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a8ba7cdf6e94c05c403bb25ed12094\transformed\plumber-android-2.14\AndroidManifest.xml:9:13-64
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e22f69e1b4a83c1f0f3b25c67a1aeb7\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d3d6fc32e7a4e9e9727494ec4e55c2a7\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e4bf12a5175260771fbec44c2236c35\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2729a180414295958aab8ce351efb7c8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
