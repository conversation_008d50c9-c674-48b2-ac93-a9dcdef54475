-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:52:9-60:20
	android:grantUriPermissions
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:56:13-47
	android:authorities
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:54:13-64
	android:exported
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:55:13-37
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:53:13-62
provider#androidx.startup.InitializationProvider
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:62:9-71:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48c9216ee5d6b429524fc837ac071821\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48c9216ee5d6b429524fc837ac071821\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:66:13-31
	android:authorities
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:64:13-68
	android:exported
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:65:13-37
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:63:13-67
manifest
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12
MERGED from [com.squareup.leakcanary:leakcanary-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\26f0d957551891f9252e1971952a8c1c\transformed\leakcanary-android-2.14\AndroidManifest.xml:17:1-22:12
MERGED from [com.meticha:permissions_compose:0.0.1+4] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a31977d094ac48bd34931d0e0d03e0\transformed\permissions_compose-0.0.1+4\AndroidManifest.xml:2:1-7:12
MERGED from [com.jvziyaoyao.scale:image-viewer:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\879b329b3cad4a599702d9a8890b408b\transformed\image-viewer-1.1.0-alpha.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.jens-muenker:uCrop-n-Edit:4.1.0-non-native] C:\Users\<USER>\.gradle\caches\8.14\transforms\5aea9c81c7615102d7b5640b77daf851\transformed\uCrop-n-Edit-4.1.0-non-native\AndroidManifest.xml:2:1-7:12
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\66672d068615dc352e3804ef30bac514\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:2:1-9:12
MERGED from [io.insert-koin:koin-androidx-compose:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\0484dfbfba2083d02252148bcc1fcfe6\transformed\koin-androidx-compose-4.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e51a321e06a9967333dcd077fe81f47\transformed\koin-android-4.0.4\AndroidManifest.xml:2:1-9:12
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:highlight] D:\FromTX3\MyCode\rikkahub\highlight\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6100cf79e00b0bf6c9c6362128e7edd6\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\31ff125282a1f0c2f1fb720115f331b3\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ea224e0fa4d1f6c64084c9f9ebce361\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.4.0-alpha14] C:\Users\<USER>\.gradle\caches\8.14\transforms\8bf40832b048d1f4d2354372c996192d\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.jvziyaoyao.scale:zoomable-view:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\3c90a3520799d480e305a95e78dfff8d\transformed\zoomable-view-1.1.0-alpha.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd7e6b47f4100bfb18caa9e6c163783\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3b6585f5ac097dc11ab8da991a5c79bd\transformed\room-ktx-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-paging-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e16c3dff21f72caa215b48446bc6098\transformed\room-paging-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.paging:paging-common-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\f18dc80549de785a65cc45ef13644be2\transformed\paging-common-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.paging:paging-runtime:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\5a81d464c6cd25d0fbbc4900493748f7\transformed\paging-runtime-3.3.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.paging:paging-compose-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\7a91c1a5c3087f7691a387c8b62ff1bc\transformed\paging-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f477ab85e7d6008688a1a0c4b191573\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\83e0db9c2519fd1246695beec68d8fbb\transformed\reorderable-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.brdominguez:compose-sonner-android:0.3.10] C:\Users\<USER>\.gradle\caches\8.14\transforms\737e518b96261b6a99ab3d69028ec1e0\transformed\sonner-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.composables:icons-lucide-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\818130c74a454861bba4dcf59c2e45c7\transformed\icons-lucide-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-compose-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\36bda59d3d688f4f4697504f1a7da0c5\transformed\coil-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-compose-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2cc9ef8018445f5abe201cb9b0267bd8\transformed\coil-compose-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\8461d9f9817ed41c58e338082d335643\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\d08bd0b429bb4ae2181000f6bdfd92e4\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a80f212a4a745d18f3ad31933ca0f653\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\eca00b7bf501269d2257285dcba25b8f\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\303c2166503144bea901100ae0f040af\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\4555af0a78ba846befe5d5d3894c4bfb\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\260352e89280195b796fb9ff51de95cc\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d5cac51cd3a09ea69407378527ea694\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\99bcbaae4dd1da62de6e7088231cdd66\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b32a2d56d63495d9b61cbc3249c6299\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\de1639b556b95b3cbd9ba5fe5f394fbb\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\af12e78fa6a38e40c61f960619544d2c\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\53ec144f1612c59cfdaa42b4b0883eab\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:2:1-22:12
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\062082a6ea1dd533c08a84778e74104d\transformed\camera-video-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-view:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\3e808f1588c4ca551309bafece8a7467\transformed\camera-view-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\bc21d47db15735bbb06c08422475cd3f\transformed\camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\efb6f60604e0ce06c685fd3c8f05289b\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c5cfe4448cac380ee1f780125e9d3d0e\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a5d5591d4acf947f8d3fdeae84598502\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b26bc0a945bb328544f80db00c932160\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c07aa272e07a450403f56e3249801207\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a91968b4dc7cf6f01038e7dd4d63f60d\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [io.coil-kt.coil3:coil-svg-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a1e633b4dfa24b2676612a7cf5b6e028\transformed\coil-svg-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\12cbf1cc5a6672ff774b72980ec369b9\transformed\coil-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\088a18d33539242d6e0101b57cb2cda2\transformed\coil-network-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1ba516b7f7e560d6a18c60f8777e4347\transformed\coil-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\35273eff1adbcd38d4c4765b01520ebc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48ffa0e1b44e6c5d14d02c946f3f174d\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\be5cc04b6a4fee62d71666ba2f889b70\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e6e7ee247c83f269d48cbfd5490c1df4\transformed\transition-1.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3df7925d68a4c42493e574a8a6301a65\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9c8f97d53ce042744bf746a99f710549\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2e6b12382a4dadf34d77e521366ccff\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\65d7c4ba4760548227d609d0a7f79fee\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\755b5a3a3bd78573c96197752104b9b1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b78d207cda22ac4214cb8630687fc1a6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\36e5e486e6ae3e9fcd826e3c14006772\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d0b006795302f793e30ff1f57492d5c8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b4ea1637f4ed07cf8df6a0d1899b880\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\37876b9c9726969f92ba09f27bc7e17f\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f84e7c2bc3fb8ce999c1fe190831cbb3\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\1b9de272188d86144f3b153ed8dee94e\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\a9e78c9af1ef0af245cc96fc37f4e886\transformed\datastore-preferences-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\5f3763780d95e37ecc89cfe19a8a789f\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\3461991fe5440ed164a37e8a7a321b1d\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\dfed636760b934f8795b2c5bd3acd456\transformed\fragment-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\82a1de50668ec17e480fc3627c1f5d2f\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e31ab92a7c70ab16f8c3b61190202e30\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8bd21c786eda4a72298f04fcac887eb4\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d11d8ffd77762e5851f69ab1b446c3c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1ddf1eda86eb44ee96c4c3d134b505d1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0a6291aa8d0f612fab01d830235388df\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fa163bb2185d04a239ce3074edb03fb3\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e22fcbbd83ad381ad62f98cd9fdd486\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7a8fa31f41e8d1bdc4ab1aa8d82d283\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d5bc7835c531e0a7f654796d9c82f3f8\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4c880877c3cc6110dc7b8718ff71d461\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2572a9edc3a1ec7bb691e6f56b52d6b3\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\824ae5c8d440c8e3d32b709ff4854e24\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d8fdb6943df83ee54604d18064797fd\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb109320d84a179bb573022d0d40b0d9\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.37.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4acd8aa78367cff7b1a338315f5f16e\transformed\accompanist-drawablepainter-0.37.3\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e1f86347c76a469161b6ee6db20a177\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9ff7994c82a27ffda7916ecd95523f61\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\69cebd4d69f1c0389795d15f2b2ecb55\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\027e2810a39435900379ec142c373d13\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\275ad86efe6174b2bf15d5f8b76e453c\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaf9750c511786fa108e7a3c496402ad\transformed\play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c9199bbd99c98990d3796f2ced5bbd81\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\6ece018d1fc01f7f9eee6a5481be4522\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2485e0fdcdff84817818dde0a5c53041\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fbb98bc7dd01984a4af3b475a4f5d5a7\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f1b106457389d514166f73b1eda76c20\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c636f8f2b207f58887a8643d65538d1e\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5dfe6173e7117d86b23fbacff5a61d1a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b63a27bb82e386a8d16cb87943712cb\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\737f4e9f9893ba45ebdf8c5bf958f65c\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8825ee7788482ab2f7bfe85f1d45ec21\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5797a51a69944b998dbc926ac41eabaa\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\807e3998bee6ca48248c93282ed09efd\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e69add8b1376897dea52ac0f7720f60\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\502909ecd9f57a85c0fdba059bb0a2c8\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\887c9c1e0bbe1248378b1eec22ea3bb5\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\005898d9c069224813d9f51552986a90\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c74d1fd39d612393ceff1070fa2d182a\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ca81321fc9e90f22fc7fbe41833df720\transformed\graphics-shapes-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f3e154f6c2797116b6a57a346ec405cd\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c91557cdd005dcdccad90e7f4f0ad9b3\transformed\core-bundle-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\376fa0245f2247ed470e2f7a402dfa14\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\30ae1fc6198264aa92f415776b75ce1a\transformed\jlatexmath-1.2\AndroidManifest.xml:2:1-14:12
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-greek:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c2572941e8b13ab9cb50214122e493fb\transformed\jlatexmath-font-greek-1.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-cyrillic:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\f8d3bf0106b5711316a54ddaa3ff5b03\transformed\jlatexmath-font-cyrillic-1.2\AndroidManifest.xml:2:1-7:12
MERGED from [:rag] D:\FromTX3\MyCode\rikkahub\rag\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:17:1-105:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:2:1-15:12
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a3aff13871f49a9dd46f24a4f57f2f9c\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd7f131a5e95c4b2674b74be78765d14\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\77a100bee5c8ad96687dd57ccdc764e8\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\495d4fe3e8f17cf960f7fa6e8364fa21\transformed\leakcanary-object-watcher-android-androidx-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a50caa613480428d40e5a208702ad131\transformed\leakcanary-object-watcher-android-support-fragments-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\c95c368326577e60c73af6c9ba10d0f5\transformed\leakcanary-object-watcher-android-core-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:plumber-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\bec9c0b98ab3ed233af6e14a5560ca35\transformed\plumber-android-core-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\511d54b94fbbdd77f26be08a0883aa32\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48c9216ee5d6b429524fc837ac071821\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\589d5fc259f2b8b7cebb012bd0c789b4\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9beb013e843bff3aec05430c18e4dd18\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.databinding:viewbinding:8.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5fc99f31fb9c3a50a5270b8ec5c0f011\transformed\viewbinding-8.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\25510e118a1d0f063f93f6502f650125\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a40e1586b6faf4cc85163ab74d290629\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0fbc835fc7e7e3dde4ecae5f435c3594\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f852ace87edd5259248a11b1354feeba\transformed\transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e14b9eefd03821def851acdcdd446ed9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3edf9120dd16b363b80f59762d761bbd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\02a224bf1e10a1e6b2e19a748804e152\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b289acd528d329bd00c795a7fecdba04\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\65988d4793852c0ef877890a60070c41\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d6ede55aed42e1d7b824c8a35c99c8ce\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1a544c71dfa2c281deacf78e2339db30\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\dccc4e8e3bb08df2b42b60a10ab2625c\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b5b96b734b15af33b70fcf70f5d2a538\transformed\exifinterface-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\442c2f2d37187c289adad614494f4973\transformed\leakcanary-android-utils-2.14\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\28d7fbd1ac252b8f8c9631a74eb34cb2\transformed\curtains-1.2.4\AndroidManifest.xml:2:1-9:12
MERGED from [io.github.oshai:kotlin-logging-android-debug:7.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\697a78fe99cefc788d060a6f7ab06099\transformed\kotlin-logging-debug\AndroidManifest.xml:2:1-7:12
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d96d1122078bc1641fedc980300f5fc\transformed\wrapper-android-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.objectbox:objectbox-android:4.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\06d10f72997c8b8decb6a47040c41d30\transformed\objectbox-android-4.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\03dedba419911b2f1fda7fb68fbcdac1\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\01a8a8cb4f77a8e08fe46e62a8745327\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.CAMERA
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:6:5-65
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:12:5-65
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:12:5-65
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:7:5-77
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:29:5-77
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:29:5-77
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:7:22-74
uses-feature#android.hardware.camera
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:9:5-11:36
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:8:5-10:36
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:8:5-10:36
	android:required
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:11:9-33
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:10:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:12:5-14:36
	android:required
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:14:9-33
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:13:9-57
application
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:16:5-72:19
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:16:5-72:19
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\66672d068615dc352e3804ef30bac514\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\66672d068615dc352e3804ef30bac514\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e51a321e06a9967333dcd077fe81f47\transformed\koin-android-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e51a321e06a9967333dcd077fe81f47\transformed\koin-android-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\af12e78fa6a38e40c61f960619544d2c\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\af12e78fa6a38e40c61f960619544d2c\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:14:5-20:19
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:14:5-20:19
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\efb6f60604e0ce06c685fd3c8f05289b\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\efb6f60604e0ce06c685fd3c8f05289b\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b26bc0a945bb328544f80db00c932160\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b26bc0a945bb328544f80db00c932160\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48ffa0e1b44e6c5d14d02c946f3f174d\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48ffa0e1b44e6c5d14d02c946f3f174d\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3df7925d68a4c42493e574a8a6301a65\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3df7925d68a4c42493e574a8a6301a65\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9c8f97d53ce042744bf746a99f710549\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9c8f97d53ce042744bf746a99f710549\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2e6b12382a4dadf34d77e521366ccff\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2e6b12382a4dadf34d77e521366ccff\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b4ea1637f4ed07cf8df6a0d1899b880\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b4ea1637f4ed07cf8df6a0d1899b880\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d11d8ffd77762e5851f69ab1b446c3c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d11d8ffd77762e5851f69ab1b446c3c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb109320d84a179bb573022d0d40b0d9\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb109320d84a179bb573022d0d40b0d9\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\69cebd4d69f1c0389795d15f2b2ecb55\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\69cebd4d69f1c0389795d15f2b2ecb55\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\027e2810a39435900379ec142c373d13\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\027e2810a39435900379ec142c373d13\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\275ad86efe6174b2bf15d5f8b76e453c\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\275ad86efe6174b2bf15d5f8b76e453c\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaf9750c511786fa108e7a3c496402ad\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaf9750c511786fa108e7a3c496402ad\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\502909ecd9f57a85c0fdba059bb0a2c8\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\502909ecd9f57a85c0fdba059bb0a2c8\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\30ae1fc6198264aa92f415776b75ce1a\transformed\jlatexmath-1.2\AndroidManifest.xml:7:5-12:19
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\30ae1fc6198264aa92f415776b75ce1a\transformed\jlatexmath-1.2\AndroidManifest.xml:7:5-12:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48c9216ee5d6b429524fc837ac071821\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48c9216ee5d6b429524fc837ac071821\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a40e1586b6faf4cc85163ab74d290629\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a40e1586b6faf4cc85163ab74d290629\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\01a8a8cb4f77a8e08fe46e62a8745327\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\01a8a8cb4f77a8e08fe46e62a8745327\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	android:appCategory
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:19:9-43
	android:icon
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:23:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:25:9-35
	android:label
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:24:9-41
	android:fullBackupContent
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:22:9-54
	tools:targetApi
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:28:9-29
	android:allowBackup
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:18:9-35
	android:theme
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:26:9-46
	android:enableOnBackInvokedCallback
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:21:9-51
	android:dataExtractionRules
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:20:9-65
	android:usesCleartextTraffic
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:27:9-44
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:17:9-36
activity#me.rerere.rikkahub.RouteActivity
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:29:9-40:20
	android:windowSoftInputMode
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:34:13-55
	android:exported
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:32:13-36
	android:configChanges
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:31:13-74
	android:theme
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:33:13-50
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:30:13-42
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:35:13-39:29
action#android.intent.action.MAIN
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:36:17-69
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:36:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:38:17-77
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:38:27-74
activity#com.yalantis.ucrop.UCropActivity
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:42:9-44:72
	android:theme
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:44:13-69
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:43:13-60
service#me.rerere.rikkahub.services.ChatService
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:46:9-50:15
	android:enabled
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:48:13-35
	android:foregroundServiceType
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:49:13-60
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:47:13-49
queries
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:74:5-78:15
intent#action:name:android.intent.action.TTS_SERVICE
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:75:9-77:18
action#android.intent.action.TTS_SERVICE
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:76:13-72
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:76:21-69
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:57:13-59:54
	android:resource
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:59:17-51
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:58:17-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:67:13-70:39
REJECTED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:70:17-36
	android:value
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:69:17-49
	android:name
		ADDED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:68:17-68
uses-sdk
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
MERGED from [com.squareup.leakcanary:leakcanary-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\26f0d957551891f9252e1971952a8c1c\transformed\leakcanary-android-2.14\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\26f0d957551891f9252e1971952a8c1c\transformed\leakcanary-android-2.14\AndroidManifest.xml:20:5-44
MERGED from [com.meticha:permissions_compose:0.0.1+4] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a31977d094ac48bd34931d0e0d03e0\transformed\permissions_compose-0.0.1+4\AndroidManifest.xml:5:5-44
MERGED from [com.meticha:permissions_compose:0.0.1+4] C:\Users\<USER>\.gradle\caches\8.14\transforms\b0a31977d094ac48bd34931d0e0d03e0\transformed\permissions_compose-0.0.1+4\AndroidManifest.xml:5:5-44
MERGED from [com.jvziyaoyao.scale:image-viewer:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\879b329b3cad4a599702d9a8890b408b\transformed\image-viewer-1.1.0-alpha.7\AndroidManifest.xml:5:5-44
MERGED from [com.jvziyaoyao.scale:image-viewer:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\879b329b3cad4a599702d9a8890b408b\transformed\image-viewer-1.1.0-alpha.7\AndroidManifest.xml:5:5-44
MERGED from [com.github.jens-muenker:uCrop-n-Edit:4.1.0-non-native] C:\Users\<USER>\.gradle\caches\8.14\transforms\5aea9c81c7615102d7b5640b77daf851\transformed\uCrop-n-Edit-4.1.0-non-native\AndroidManifest.xml:5:5-44
MERGED from [com.github.jens-muenker:uCrop-n-Edit:4.1.0-non-native] C:\Users\<USER>\.gradle\caches\8.14\transforms\5aea9c81c7615102d7b5640b77daf851\transformed\uCrop-n-Edit-4.1.0-non-native\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\66672d068615dc352e3804ef30bac514\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\66672d068615dc352e3804ef30bac514\transformed\koin-androidx-workmanager-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-compose:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\0484dfbfba2083d02252148bcc1fcfe6\transformed\koin-androidx-compose-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-compose:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\0484dfbfba2083d02252148bcc1fcfe6\transformed\koin-androidx-compose-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e51a321e06a9967333dcd077fe81f47\transformed\koin-android-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8e51a321e06a9967333dcd077fe81f47\transformed\koin-android-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:ai] D:\FromTX3\MyCode\rikkahub\ai\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:highlight] D:\FromTX3\MyCode\rikkahub\highlight\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:highlight] D:\FromTX3\MyCode\rikkahub\highlight\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:search] D:\FromTX3\MyCode\rikkahub\search\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6100cf79e00b0bf6c9c6362128e7edd6\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6100cf79e00b0bf6c9c6362128e7edd6\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\31ff125282a1f0c2f1fb720115f331b3\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\31ff125282a1f0c2f1fb720115f331b3\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ea224e0fa4d1f6c64084c9f9ebce361\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ea224e0fa4d1f6c64084c9f9ebce361\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.4.0-alpha14] C:\Users\<USER>\.gradle\caches\8.14\transforms\8bf40832b048d1f4d2354372c996192d\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.4.0-alpha14] C:\Users\<USER>\.gradle\caches\8.14\transforms\8bf40832b048d1f4d2354372c996192d\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [com.jvziyaoyao.scale:zoomable-view:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\3c90a3520799d480e305a95e78dfff8d\transformed\zoomable-view-1.1.0-alpha.7\AndroidManifest.xml:5:5-44
MERGED from [com.jvziyaoyao.scale:zoomable-view:1.1.0-alpha.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\3c90a3520799d480e305a95e78dfff8d\transformed\zoomable-view-1.1.0-alpha.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd7e6b47f4100bfb18caa9e6c163783\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd7e6b47f4100bfb18caa9e6c163783\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3b6585f5ac097dc11ab8da991a5c79bd\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3b6585f5ac097dc11ab8da991a5c79bd\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-paging-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e16c3dff21f72caa215b48446bc6098\transformed\room-paging-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-paging-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e16c3dff21f72caa215b48446bc6098\transformed\room-paging-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.paging:paging-common-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\f18dc80549de785a65cc45ef13644be2\transformed\paging-common-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-common-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\f18dc80549de785a65cc45ef13644be2\transformed\paging-common-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-runtime:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\5a81d464c6cd25d0fbbc4900493748f7\transformed\paging-runtime-3.3.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-runtime:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\5a81d464c6cd25d0fbbc4900493748f7\transformed\paging-runtime-3.3.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-compose-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\7a91c1a5c3087f7691a387c8b62ff1bc\transformed\paging-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-compose-android:3.3.6] C:\Users\<USER>\.gradle\caches\8.14\transforms\7a91c1a5c3087f7691a387c8b62ff1bc\transformed\paging-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f477ab85e7d6008688a1a0c4b191573\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f477ab85e7d6008688a1a0c4b191573\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\83e0db9c2519fd1246695beec68d8fbb\transformed\reorderable-debug\AndroidManifest.xml:5:5-44
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\83e0db9c2519fd1246695beec68d8fbb\transformed\reorderable-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.brdominguez:compose-sonner-android:0.3.10] C:\Users\<USER>\.gradle\caches\8.14\transforms\737e518b96261b6a99ab3d69028ec1e0\transformed\sonner-release\AndroidManifest.xml:5:5-43
MERGED from [io.github.brdominguez:compose-sonner-android:0.3.10] C:\Users\<USER>\.gradle\caches\8.14\transforms\737e518b96261b6a99ab3d69028ec1e0\transformed\sonner-release\AndroidManifest.xml:5:5-43
MERGED from [com.composables:icons-lucide-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\818130c74a454861bba4dcf59c2e45c7\transformed\icons-lucide-debug\AndroidManifest.xml:5:5-44
MERGED from [com.composables:icons-lucide-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\818130c74a454861bba4dcf59c2e45c7\transformed\icons-lucide-debug\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\36bda59d3d688f4f4697504f1a7da0c5\transformed\coil-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\36bda59d3d688f4f4697504f1a7da0c5\transformed\coil-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2cc9ef8018445f5abe201cb9b0267bd8\transformed\coil-compose-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2cc9ef8018445f5abe201cb9b0267bd8\transformed\coil-compose-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\8461d9f9817ed41c58e338082d335643\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\8461d9f9817ed41c58e338082d335643\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\d08bd0b429bb4ae2181000f6bdfd92e4\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\d08bd0b429bb4ae2181000f6bdfd92e4\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a80f212a4a745d18f3ad31933ca0f653\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a80f212a4a745d18f3ad31933ca0f653\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\eca00b7bf501269d2257285dcba25b8f\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\eca00b7bf501269d2257285dcba25b8f\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\303c2166503144bea901100ae0f040af\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\303c2166503144bea901100ae0f040af\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\4555af0a78ba846befe5d5d3894c4bfb\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\4555af0a78ba846befe5d5d3894c4bfb\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\260352e89280195b796fb9ff51de95cc\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\260352e89280195b796fb9ff51de95cc\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d5cac51cd3a09ea69407378527ea694\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d5cac51cd3a09ea69407378527ea694\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\99bcbaae4dd1da62de6e7088231cdd66\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\99bcbaae4dd1da62de6e7088231cdd66\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b32a2d56d63495d9b61cbc3249c6299\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b32a2d56d63495d9b61cbc3249c6299\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\de1639b556b95b3cbd9ba5fe5f394fbb\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\de1639b556b95b3cbd9ba5fe5f394fbb\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\af12e78fa6a38e40c61f960619544d2c\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\af12e78fa6a38e40c61f960619544d2c\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\53ec144f1612c59cfdaa42b4b0883eab\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\53ec144f1612c59cfdaa42b4b0883eab\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:6:5-44
MERGED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\062082a6ea1dd533c08a84778e74104d\transformed\camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\062082a6ea1dd533c08a84778e74104d\transformed\camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\3e808f1588c4ca551309bafece8a7467\transformed\camera-view-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\3e808f1588c4ca551309bafece8a7467\transformed\camera-view-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\bc21d47db15735bbb06c08422475cd3f\transformed\camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\bc21d47db15735bbb06c08422475cd3f\transformed\camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\efb6f60604e0ce06c685fd3c8f05289b\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\efb6f60604e0ce06c685fd3c8f05289b\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c5cfe4448cac380ee1f780125e9d3d0e\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c5cfe4448cac380ee1f780125e9d3d0e\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a5d5591d4acf947f8d3fdeae84598502\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a5d5591d4acf947f8d3fdeae84598502\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b26bc0a945bb328544f80db00c932160\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b26bc0a945bb328544f80db00c932160\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c07aa272e07a450403f56e3249801207\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c07aa272e07a450403f56e3249801207\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a91968b4dc7cf6f01038e7dd4d63f60d\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a91968b4dc7cf6f01038e7dd4d63f60d\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [io.coil-kt.coil3:coil-svg-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a1e633b4dfa24b2676612a7cf5b6e028\transformed\coil-svg-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-svg-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a1e633b4dfa24b2676612a7cf5b6e028\transformed\coil-svg-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\12cbf1cc5a6672ff774b72980ec369b9\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\12cbf1cc5a6672ff774b72980ec369b9\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\088a18d33539242d6e0101b57cb2cda2\transformed\coil-network-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\088a18d33539242d6e0101b57cb2cda2\transformed\coil-network-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1ba516b7f7e560d6a18c60f8777e4347\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1ba516b7f7e560d6a18c60f8777e4347\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\35273eff1adbcd38d4c4765b01520ebc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\35273eff1adbcd38d4c4765b01520ebc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48ffa0e1b44e6c5d14d02c946f3f174d\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48ffa0e1b44e6c5d14d02c946f3f174d\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\be5cc04b6a4fee62d71666ba2f889b70\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\be5cc04b6a4fee62d71666ba2f889b70\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e6e7ee247c83f269d48cbfd5490c1df4\transformed\transition-1.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e6e7ee247c83f269d48cbfd5490c1df4\transformed\transition-1.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3df7925d68a4c42493e574a8a6301a65\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3df7925d68a4c42493e574a8a6301a65\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9c8f97d53ce042744bf746a99f710549\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9c8f97d53ce042744bf746a99f710549\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2e6b12382a4dadf34d77e521366ccff\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2e6b12382a4dadf34d77e521366ccff\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\65d7c4ba4760548227d609d0a7f79fee\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\65d7c4ba4760548227d609d0a7f79fee\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\755b5a3a3bd78573c96197752104b9b1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\755b5a3a3bd78573c96197752104b9b1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b78d207cda22ac4214cb8630687fc1a6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b78d207cda22ac4214cb8630687fc1a6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\36e5e486e6ae3e9fcd826e3c14006772\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\36e5e486e6ae3e9fcd826e3c14006772\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d0b006795302f793e30ff1f57492d5c8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d0b006795302f793e30ff1f57492d5c8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b4ea1637f4ed07cf8df6a0d1899b880\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2b4ea1637f4ed07cf8df6a0d1899b880\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\37876b9c9726969f92ba09f27bc7e17f\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\37876b9c9726969f92ba09f27bc7e17f\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f84e7c2bc3fb8ce999c1fe190831cbb3\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f84e7c2bc3fb8ce999c1fe190831cbb3\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\1b9de272188d86144f3b153ed8dee94e\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\1b9de272188d86144f3b153ed8dee94e\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\a9e78c9af1ef0af245cc96fc37f4e886\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\a9e78c9af1ef0af245cc96fc37f4e886\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\5f3763780d95e37ecc89cfe19a8a789f\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\5f3763780d95e37ecc89cfe19a8a789f\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\3461991fe5440ed164a37e8a7a321b1d\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\3461991fe5440ed164a37e8a7a321b1d\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\dfed636760b934f8795b2c5bd3acd456\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\dfed636760b934f8795b2c5bd3acd456\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\82a1de50668ec17e480fc3627c1f5d2f\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.14\transforms\82a1de50668ec17e480fc3627c1f5d2f\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e31ab92a7c70ab16f8c3b61190202e30\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e31ab92a7c70ab16f8c3b61190202e30\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8bd21c786eda4a72298f04fcac887eb4\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8bd21c786eda4a72298f04fcac887eb4\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d11d8ffd77762e5851f69ab1b446c3c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d11d8ffd77762e5851f69ab1b446c3c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1ddf1eda86eb44ee96c4c3d134b505d1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1ddf1eda86eb44ee96c4c3d134b505d1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0a6291aa8d0f612fab01d830235388df\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0a6291aa8d0f612fab01d830235388df\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fa163bb2185d04a239ce3074edb03fb3\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fa163bb2185d04a239ce3074edb03fb3\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e22fcbbd83ad381ad62f98cd9fdd486\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e22fcbbd83ad381ad62f98cd9fdd486\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7a8fa31f41e8d1bdc4ab1aa8d82d283\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7a8fa31f41e8d1bdc4ab1aa8d82d283\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d5bc7835c531e0a7f654796d9c82f3f8\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d5bc7835c531e0a7f654796d9c82f3f8\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4c880877c3cc6110dc7b8718ff71d461\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4c880877c3cc6110dc7b8718ff71d461\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2572a9edc3a1ec7bb691e6f56b52d6b3\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2572a9edc3a1ec7bb691e6f56b52d6b3\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\824ae5c8d440c8e3d32b709ff4854e24\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\824ae5c8d440c8e3d32b709ff4854e24\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d8fdb6943df83ee54604d18064797fd\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d8fdb6943df83ee54604d18064797fd\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb109320d84a179bb573022d0d40b0d9\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb109320d84a179bb573022d0d40b0d9\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.37.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4acd8aa78367cff7b1a338315f5f16e\transformed\accompanist-drawablepainter-0.37.3\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.37.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4acd8aa78367cff7b1a338315f5f16e\transformed\accompanist-drawablepainter-0.37.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e1f86347c76a469161b6ee6db20a177\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e1f86347c76a469161b6ee6db20a177\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9ff7994c82a27ffda7916ecd95523f61\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9ff7994c82a27ffda7916ecd95523f61\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\69cebd4d69f1c0389795d15f2b2ecb55\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\69cebd4d69f1c0389795d15f2b2ecb55\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\027e2810a39435900379ec142c373d13\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\027e2810a39435900379ec142c373d13\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\275ad86efe6174b2bf15d5f8b76e453c\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\275ad86efe6174b2bf15d5f8b76e453c\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaf9750c511786fa108e7a3c496402ad\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaf9750c511786fa108e7a3c496402ad\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c9199bbd99c98990d3796f2ced5bbd81\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c9199bbd99c98990d3796f2ced5bbd81\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\6ece018d1fc01f7f9eee6a5481be4522\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\6ece018d1fc01f7f9eee6a5481be4522\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2485e0fdcdff84817818dde0a5c53041\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2485e0fdcdff84817818dde0a5c53041\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fbb98bc7dd01984a4af3b475a4f5d5a7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fbb98bc7dd01984a4af3b475a4f5d5a7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f1b106457389d514166f73b1eda76c20\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f1b106457389d514166f73b1eda76c20\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c636f8f2b207f58887a8643d65538d1e\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c636f8f2b207f58887a8643d65538d1e\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5dfe6173e7117d86b23fbacff5a61d1a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5dfe6173e7117d86b23fbacff5a61d1a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b63a27bb82e386a8d16cb87943712cb\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5b63a27bb82e386a8d16cb87943712cb\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\737f4e9f9893ba45ebdf8c5bf958f65c\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\737f4e9f9893ba45ebdf8c5bf958f65c\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8825ee7788482ab2f7bfe85f1d45ec21\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8825ee7788482ab2f7bfe85f1d45ec21\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5797a51a69944b998dbc926ac41eabaa\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5797a51a69944b998dbc926ac41eabaa\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\807e3998bee6ca48248c93282ed09efd\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\807e3998bee6ca48248c93282ed09efd\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e69add8b1376897dea52ac0f7720f60\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\4e69add8b1376897dea52ac0f7720f60\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\502909ecd9f57a85c0fdba059bb0a2c8\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\502909ecd9f57a85c0fdba059bb0a2c8\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\887c9c1e0bbe1248378b1eec22ea3bb5\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\887c9c1e0bbe1248378b1eec22ea3bb5\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\005898d9c069224813d9f51552986a90\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\005898d9c069224813d9f51552986a90\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c74d1fd39d612393ceff1070fa2d182a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c74d1fd39d612393ceff1070fa2d182a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ca81321fc9e90f22fc7fbe41833df720\transformed\graphics-shapes-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ca81321fc9e90f22fc7fbe41833df720\transformed\graphics-shapes-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f3e154f6c2797116b6a57a346ec405cd\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f3e154f6c2797116b6a57a346ec405cd\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c91557cdd005dcdccad90e7f4f0ad9b3\transformed\core-bundle-debug\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c91557cdd005dcdccad90e7f4f0ad9b3\transformed\core-bundle-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\376fa0245f2247ed470e2f7a402dfa14\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\376fa0245f2247ed470e2f7a402dfa14\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\30ae1fc6198264aa92f415776b75ce1a\transformed\jlatexmath-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\30ae1fc6198264aa92f415776b75ce1a\transformed\jlatexmath-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-greek:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c2572941e8b13ab9cb50214122e493fb\transformed\jlatexmath-font-greek-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-greek:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c2572941e8b13ab9cb50214122e493fb\transformed\jlatexmath-font-greek-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-cyrillic:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\f8d3bf0106b5711316a54ddaa3ff5b03\transformed\jlatexmath-font-cyrillic-1.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.rikkahub.jlatexmath-android:jlatexmath-font-cyrillic:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\f8d3bf0106b5711316a54ddaa3ff5b03\transformed\jlatexmath-font-cyrillic-1.2\AndroidManifest.xml:5:5-44
MERGED from [:rag] D:\FromTX3\MyCode\rikkahub\rag\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rag] D:\FromTX3\MyCode\rikkahub\rag\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a3aff13871f49a9dd46f24a4f57f2f9c\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a3aff13871f49a9dd46f24a4f57f2f9c\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd7f131a5e95c4b2674b74be78765d14\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd7f131a5e95c4b2674b74be78765d14\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\77a100bee5c8ad96687dd57ccdc764e8\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\77a100bee5c8ad96687dd57ccdc764e8\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\495d4fe3e8f17cf960f7fa6e8364fa21\transformed\leakcanary-object-watcher-android-androidx-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\495d4fe3e8f17cf960f7fa6e8364fa21\transformed\leakcanary-object-watcher-android-androidx-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a50caa613480428d40e5a208702ad131\transformed\leakcanary-object-watcher-android-support-fragments-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\a50caa613480428d40e5a208702ad131\transformed\leakcanary-object-watcher-android-support-fragments-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\c95c368326577e60c73af6c9ba10d0f5\transformed\leakcanary-object-watcher-android-core-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\c95c368326577e60c73af6c9ba10d0f5\transformed\leakcanary-object-watcher-android-core-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\bec9c0b98ab3ed233af6e14a5560ca35\transformed\plumber-android-core-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\bec9c0b98ab3ed233af6e14a5560ca35\transformed\plumber-android-core-2.14\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\511d54b94fbbdd77f26be08a0883aa32\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\511d54b94fbbdd77f26be08a0883aa32\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48c9216ee5d6b429524fc837ac071821\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\48c9216ee5d6b429524fc837ac071821\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\589d5fc259f2b8b7cebb012bd0c789b4\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\589d5fc259f2b8b7cebb012bd0c789b4\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9beb013e843bff3aec05430c18e4dd18\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9beb013e843bff3aec05430c18e4dd18\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.databinding:viewbinding:8.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5fc99f31fb9c3a50a5270b8ec5c0f011\transformed\viewbinding-8.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5fc99f31fb9c3a50a5270b8ec5c0f011\transformed\viewbinding-8.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\25510e118a1d0f063f93f6502f650125\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\25510e118a1d0f063f93f6502f650125\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a40e1586b6faf4cc85163ab74d290629\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a40e1586b6faf4cc85163ab74d290629\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0fbc835fc7e7e3dde4ecae5f435c3594\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0fbc835fc7e7e3dde4ecae5f435c3594\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f852ace87edd5259248a11b1354feeba\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f852ace87edd5259248a11b1354feeba\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e14b9eefd03821def851acdcdd446ed9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e14b9eefd03821def851acdcdd446ed9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3edf9120dd16b363b80f59762d761bbd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3edf9120dd16b363b80f59762d761bbd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\02a224bf1e10a1e6b2e19a748804e152\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\02a224bf1e10a1e6b2e19a748804e152\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b289acd528d329bd00c795a7fecdba04\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b289acd528d329bd00c795a7fecdba04\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\65988d4793852c0ef877890a60070c41\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\65988d4793852c0ef877890a60070c41\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d6ede55aed42e1d7b824c8a35c99c8ce\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d6ede55aed42e1d7b824c8a35c99c8ce\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1a544c71dfa2c281deacf78e2339db30\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1a544c71dfa2c281deacf78e2339db30\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\dccc4e8e3bb08df2b42b60a10ab2625c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\dccc4e8e3bb08df2b42b60a10ab2625c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b5b96b734b15af33b70fcf70f5d2a538\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b5b96b734b15af33b70fcf70f5d2a538\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\442c2f2d37187c289adad614494f4973\transformed\leakcanary-android-utils-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\442c2f2d37187c289adad614494f4973\transformed\leakcanary-android-utils-2.14\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\28d7fbd1ac252b8f8c9631a74eb34cb2\transformed\curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\28d7fbd1ac252b8f8c9631a74eb34cb2\transformed\curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.oshai:kotlin-logging-android-debug:7.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\697a78fe99cefc788d060a6f7ab06099\transformed\kotlin-logging-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.oshai:kotlin-logging-android-debug:7.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\697a78fe99cefc788d060a6f7ab06099\transformed\kotlin-logging-debug\AndroidManifest.xml:5:5-44
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d96d1122078bc1641fedc980300f5fc\transformed\wrapper-android-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7d96d1122078bc1641fedc980300f5fc\transformed\wrapper-android-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [io.objectbox:objectbox-android:4.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\06d10f72997c8b8decb6a47040c41d30\transformed\objectbox-android-4.3.0\AndroidManifest.xml:6:5-44
MERGED from [io.objectbox:objectbox-android:4.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\06d10f72997c8b8decb6a47040c41d30\transformed\objectbox-android-4.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\03dedba419911b2f1fda7fb68fbcdac1\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\03dedba419911b2f1fda7fb68fbcdac1\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\01a8a8cb4f77a8e08fe46e62a8745327\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14\transforms\01a8a8cb4f77a8e08fe46e62a8745327\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b8d754eb5303359053679da12bad27ab\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\307acb44cb37c06e7bc4ee2b79d6e272\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
IMPLIED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12 reason: com.dokar.sonner.core has a targetSdkVersion < 4
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:26:5-81
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:26:5-81
uses-permission#android.permission.READ_PHONE_STATE
IMPLIED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12 reason: com.dokar.sonner.core has a targetSdkVersion < 4
uses-permission#android.permission.READ_EXTERNAL_STORAGE
IMPLIED from D:\FromTX3\MyCode\rikkahub\app\src\main\AndroidManifest.xml:2:1-79:12 reason: com.dokar.sonner.core requested WRITE_EXTERNAL_STORAGE
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:25:5-80
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:25:5-80
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\af12e78fa6a38e40c61f960619544d2c\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\af12e78fa6a38e40c61f960619544d2c\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\af12e78fa6a38e40c61f960619544d2c\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#io.github.g00fy2.quickie.QRScannerActivity
ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:15:9-19:45
	android:screenOrientation
		ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:17:13-47
	tools:ignore
		ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:19:13-42
	android:theme
		ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:18:13-58
	android:name
		ADDED from [io.github.g00fy2.quickie:quickie-bundled:1.10.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\835032a51b5d40afa0ab30aa91122c7e\transformed\quickie-bundled-1.10.0\AndroidManifest.xml:16:13-70
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\efb6f60604e0ce06c685fd3c8f05289b\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\efb6f60604e0ce06c685fd3c8f05289b\transformed\camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14\transforms\37403c5423fa486ca75bc2182b3d80ef\transformed\camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b45274a1108cb5962bf3d4d7dde762eb\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e080b03be28cf9eeb73e8b87439a19d3\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\572e9af60e4b0826f36123f254f0b918\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a508fc0a24a40bfb8f3e42948d284cc3\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\027e2810a39435900379ec142c373d13\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\027e2810a39435900379ec142c373d13\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8222800d34225e5d53b0a58e4848d70\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:22-76
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2e6b12382a4dadf34d77e521366ccff\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2e6b12382a4dadf34d77e521366ccff\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2e6b12382a4dadf34d77e521366ccff\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2e6b12382a4dadf34d77e521366ccff\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cc314de5e0a60587a5ff0bd182b42930\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abb61a3860d3ee605cf514b1a0340309\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c1f264151833f086ef21980287101ae\transformed\firebase-crashlytics-19.4.3\AndroidManifest.xml:19:17-115
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\514629a3359978e79d1ca1ed7dfa43f6\transformed\firebase-sessions-2.1.1\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9f66a820b19889dd7f261d63b73fdee8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8924cd7d28f98ed709d5cae672429a89\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5d2526bdb4cc3d9be6343348ece9990a\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7bef27de9d3b6d7838499350629c9f65\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aba4e1137b1f9e2dc7ab9f00a85130ca\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb109320d84a179bb573022d0d40b0d9\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb109320d84a179bb573022d0d40b0d9\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\cb109320d84a179bb573022d0d40b0d9\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaf9750c511786fa108e7a3c496402ad\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaf9750c511786fa108e7a3c496402ad\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aaf9750c511786fa108e7a3c496402ad\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#me.rerere.rikkahub.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#me.rerere.rikkahub.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d642686678dc9e4dc645c444fc2595b8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\502909ecd9f57a85c0fdba059bb0a2c8\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\502909ecd9f57a85c0fdba059bb0a2c8\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\502909ecd9f57a85c0fdba059bb0a2c8\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\502909ecd9f57a85c0fdba059bb0a2c8\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
provider#ru.noties.jlatexmath.JLatexMathInitProvider
ADDED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\30ae1fc6198264aa92f415776b75ce1a\transformed\jlatexmath-1.2\AndroidManifest.xml:8:9-11:40
	android:authorities
		ADDED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\30ae1fc6198264aa92f415776b75ce1a\transformed\jlatexmath-1.2\AndroidManifest.xml:10:13-74
	android:exported
		ADDED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\30ae1fc6198264aa92f415776b75ce1a\transformed\jlatexmath-1.2\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [com.github.rikkahub.jlatexmath-android:jlatexmath:1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\30ae1fc6198264aa92f415776b75ce1a\transformed\jlatexmath-1.2\AndroidManifest.xml:9:13-71
provider#leakcanary.internal.LeakCanaryFileProvider
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:32:9-40:20
	android:grantUriPermissions
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:36:13-47
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:34:13-88
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:33:13-70
activity#leakcanary.internal.activity.LeakActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:42:9-73:20
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:46:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:44:13-36
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:45:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:48:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:47:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:43:13-69
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:mimeType:*/*+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\.hprof+data:pathPattern:.*\\.hprof+data:scheme:content+data:scheme:file
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:49:13-72:29
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:49:28-81
action#android.intent.action.VIEW
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:50:17-69
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:50:25-66
category#android.intent.category.DEFAULT
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:52:17-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:52:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:53:17-78
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:53:27-75
data
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:17-47
	android:scheme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:55:23-44
activity-alias#leakcanary.internal.activity.LeakLauncherActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:75:9-92:26
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:78:13-66
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:81:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:79:13-36
	android:targetActivity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:82:13-79
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:80:13-52
	android:banner
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:77:13-59
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:84:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:83:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:76:13-77
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:85:13-91:29
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:90:17-86
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:90:27-83
activity#leakcanary.internal.RequestPermissionActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:94:9-100:68
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:98:13-82
	android:excludeFromRecents
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:96:13-46
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:97:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:100:13-65
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:99:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:95:13-73
receiver#leakcanary.internal.NotificationReceiver
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:102:9-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ee0524a935fe4c924df4ba2e4e24000\transformed\leakcanary-android-core-2.14\AndroidManifest.xml:102:19-74
provider#leakcanary.internal.MainProcessAppWatcherInstaller
ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e3f2c5e9beccbf3f25b116a07f38db6\transformed\leakcanary-object-watcher-android-2.14\AndroidManifest.xml:9:13-78
provider#leakcanary.internal.PlumberInstaller
ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:10:13-69
	android:exported
		ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:plumber-android:2.14] C:\Users\<USER>\.gradle\caches\8.14\transforms\d7ab330357aa20624881ed94e7768b51\transformed\plumber-android-2.14\AndroidManifest.xml:9:13-64
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8ef17fc16bfe91eb31d219e4b46ebc88\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\747894d7d74517e483104ad4c1b8082a\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f75460f1304c87ed859fccc35e9ce06\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3eb579b2c0d18c3992cfe831eb9ca7b8\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
