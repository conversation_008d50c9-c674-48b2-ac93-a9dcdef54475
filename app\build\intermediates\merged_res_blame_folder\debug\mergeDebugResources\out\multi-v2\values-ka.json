{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b26bc0a945bb328544f80db00c932160\\transformed\\material-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1071,1138,1235,1304,1367,1454,1518,1584,1644,1713,1774,1828,1943,2002,2062,2116,2188,2318,2406,2485,2583,2671,2755,2893,2971,3047,3186,3280,3360,3416,3470,3536,3609,3687,3758,3842,3915,3993,4066,4141,4251,4341,4416,4510,4608,4682,4759,4859,4912,4996,5064,5153,5242,5304,5369,5432,5502,5609,5709,5809,5905,5965,6023,6103,6193,6268", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "268,346,420,504,597,691,790,915,1003,1066,1133,1230,1299,1362,1449,1513,1579,1639,1708,1769,1823,1938,1997,2057,2111,2183,2313,2401,2480,2578,2666,2750,2888,2966,3042,3181,3275,3355,3411,3465,3531,3604,3682,3753,3837,3910,3988,4061,4136,4246,4336,4411,4505,4603,4677,4754,4854,4907,4991,5059,5148,5237,5299,5364,5427,5497,5604,5704,5804,5900,5960,6018,6098,6188,6263,6344"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3111,3189,3263,3347,3440,4258,4357,4482,7264,7327,7394,7491,7736,15073,15160,15224,15290,15350,15419,15480,15534,15649,15708,15768,15822,15894,16118,16206,16285,16383,16471,16555,16693,16771,16847,16986,17080,17160,17216,17270,17336,17409,17487,17558,17642,17715,17793,17866,17941,18051,18141,18216,18310,18408,18482,18559,18659,18712,18796,18864,18953,19042,19104,19169,19232,19302,19409,19509,19609,19705,19765,20000,20480,20570,20717", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "318,3184,3258,3342,3435,3529,4352,4477,4565,7322,7389,7486,7555,7794,15155,15219,15285,15345,15414,15475,15529,15644,15703,15763,15817,15889,16019,16201,16280,16378,16466,16550,16688,16766,16842,16981,17075,17155,17211,17265,17331,17404,17482,17553,17637,17710,17788,17861,17936,18046,18136,18211,18305,18403,18477,18554,18654,18707,18791,18859,18948,19037,19099,19164,19227,19297,19404,19504,19604,19700,19760,19818,20075,20565,20640,20793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8bf40832b048d1f4d2354372c996192d\\transformed\\material3-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,411,525,625,724,840,976,1094,1242,1328,1430,1524,1622,1744,1864,1971,2106,2243,2378,2550,2679,2796,2914,3035,3130,3227,3345,3484,3587,3689,3800,3938,4078,4189,4292,4369,4464,4562,4672,4785,4899,4985,5072,5166,5279,5378,5458,5557,5642,5743,5846,5940,6042,6128,6234,6330,6438,6555,6635,6778,6908,7014,7111,7219", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,112,113,85,86,93,112,98,79,98,84,100,102,93,101,85,105,95,107,116,79,142,129,105,96,107,109", "endOffsets": "168,285,406,520,620,719,835,971,1089,1237,1323,1425,1519,1617,1739,1859,1966,2101,2238,2373,2545,2674,2791,2909,3030,3125,3222,3340,3479,3582,3684,3795,3933,4073,4184,4287,4364,4459,4557,4667,4780,4894,4980,5067,5161,5274,5373,5453,5552,5637,5738,5841,5935,6037,6123,6229,6325,6433,6550,6630,6773,6903,7009,7106,7214,7324"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7799,7917,8034,8155,8269,8369,8468,8584,8720,8838,8986,9072,9174,9268,9366,9488,9608,9715,9850,9987,10122,10294,10423,10540,10658,10779,10874,10971,11089,11228,11331,11433,11544,11682,11822,11933,12036,12113,12208,12306,12416,12529,12643,12729,12816,12910,13023,13122,13202,13301,13386,13487,13590,13684,13786,13872,13978,14074,14182,14299,14379,14522,14652,14758,14855,14963", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,112,113,85,86,93,112,98,79,98,84,100,102,93,101,85,105,95,107,116,79,142,129,105,96,107,109", "endOffsets": "7912,8029,8150,8264,8364,8463,8579,8715,8833,8981,9067,9169,9263,9361,9483,9603,9710,9845,9982,10117,10289,10418,10535,10653,10774,10869,10966,11084,11223,11326,11428,11539,11677,11817,11928,12031,12108,12203,12301,12411,12524,12638,12724,12811,12905,13018,13117,13197,13296,13381,13482,13585,13679,13781,13867,13973,14069,14177,14294,14374,14517,14647,14753,14850,14958,15068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d2e6b12382a4dadf34d77e521366ccff\\transformed\\play-services-base-18.5.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4752,4857,5006,5134,5244,5398,5532,5654,5905,6078,6186,6341,6469,6630,6769,6835,6896", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "4852,5001,5129,5239,5393,5527,5649,5757,6073,6181,6336,6464,6625,6764,6830,6891,6967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4e69add8b1376897dea52ac0f7720f60\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,287,373,472,575,665,745,841,931,1018,1107,1198,1270,1360,1438,1516,1591,1670,1740", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "282,368,467,570,660,740,836,926,1013,1102,1193,1265,1355,1433,1511,1586,1665,1735,1856"}, "to": {"startLines": "49,50,69,70,71,76,77,204,205,209,210,214,216,217,218,219,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4570,4666,6972,7071,7174,7560,7640,19823,19913,20218,20307,20645,20798,20888,20966,21044,21220,21299,21369", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "4661,4747,7066,7169,7259,7635,7731,19908,19995,20302,20393,20712,20883,20961,21039,21114,21294,21364,21485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\aaf9750c511786fa108e7a3c496402ad\\transformed\\play-services-basement-18.5.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5762", "endColumns": "142", "endOffsets": "5900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\be5cc04b6a4fee62d71666ba2f889b70\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,20398", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,20475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\303c2166503144bea901100ae0f040af\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,228", "endColumns": "84,87,90", "endOffsets": "135,223,314"}, "to": {"startLines": "33,224,225", "startColumns": "4,4,4", "startOffsets": "3026,21490,21578", "endColumns": "84,87,90", "endOffsets": "3106,21573,21664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2f477ab85e7d6008688a1a0c4b191573\\transformed\\material-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "16024", "endColumns": "93", "endOffsets": "16113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\835032a51b5d40afa0ab30aa91122c7e\\transformed\\quickie-bundled-1.10.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,123", "endColumns": "67,69", "endOffsets": "118,188"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "20080,20148", "endColumns": "67,69", "endOffsets": "20143,20213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d642686678dc9e4dc645c444fc2595b8\\transformed\\core-1.16.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "39,40,41,42,43,44,45,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3534,3630,3732,3831,3930,4036,4140,21119", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3625,3727,3826,3925,4031,4135,4253,21215"}}]}]}