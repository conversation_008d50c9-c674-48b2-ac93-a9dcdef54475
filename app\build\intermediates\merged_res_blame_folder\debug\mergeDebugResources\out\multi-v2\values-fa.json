{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f58a4462c0f661bc016516b04b05ffbf\\transformed\\core-1.16.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "39,40,41,42,43,44,45,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3492,3591,3693,3792,3892,3993,4099,20708", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3586,3688,3787,3887,3988,4094,4211,20804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\96abb1e1502d74c75d73c35e14f81e22\\transformed\\play-services-basement-18.5.0\\res\\values-fa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "150", "endOffsets": "345"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5671", "endColumns": "154", "endOffsets": "5821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c0ee106e096d6fe0bd1735cc2a0a4c89\\transformed\\quickie-bundled-1.10.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,72", "endOffsets": "116,189"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "19708,19774", "endColumns": "65,72", "endOffsets": "19769,19842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\757e326fd7991b30514ac12e75a7701a\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,419,520,631,715,816,931,1011,1088,1181,1276,1368,1462,1564,1659,1756,1850,1943,2033,2115,2223,2327,2425,2531,2636,2741,2898,20010", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "414,515,626,710,811,926,1006,1083,1176,1271,1363,1457,1559,1654,1751,1845,1938,2028,2110,2218,2322,2420,2526,2631,2736,2893,2994,20087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9436b4ebc37cac3d97710224e74d8985\\transformed\\play-services-base-18.5.0\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,450,575,674,810,932,1042,1140,1289,1395,1561,1688,1837,1989,2051,2115", "endColumns": "103,152,124,98,135,121,109,97,148,105,165,126,148,151,61,63,80", "endOffsets": "296,449,574,673,809,931,1041,1139,1288,1394,1560,1687,1836,1988,2050,2114,2195"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4692,4800,4957,5086,5189,5329,5455,5569,5826,5979,6089,6259,6390,6543,6699,6765,6833", "endColumns": "107,156,128,102,139,125,113,101,152,109,169,130,152,155,65,67,84", "endOffsets": "4795,4952,5081,5184,5324,5450,5564,5666,5974,6084,6254,6385,6538,6694,6760,6828,6913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\e744a31c82164d30026af487ba5e43bf\\transformed\\uCrop-n-Edit-4.1.0-non-native\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,98,160,214,262,308", "endColumns": "42,61,53,47,45,46", "endOffsets": "93,155,209,257,303,350"}, "to": {"startLines": "226,227,228,229,230,231", "startColumns": "4,4,4,4,4,4", "startOffsets": "21246,21289,21351,21405,21453,21499", "endColumns": "42,61,53,47,45,46", "endOffsets": "21284,21346,21400,21448,21494,21541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\ec01753cc085eccabdf318240ae84988\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,272,351,445,543,629,711,814,899,982,1063,1145,1219,1303,1378,1452,1524,1599,1666", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "267,346,440,538,624,706,809,894,977,1058,1140,1214,1298,1373,1447,1519,1594,1661,1778"}, "to": {"startLines": "49,50,69,70,71,76,77,204,205,209,210,214,216,217,218,219,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4526,4613,6918,7012,7110,7476,7558,19462,19547,19847,19928,20255,20403,20487,20562,20636,20809,20884,20951", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "4608,4687,7007,7105,7191,7553,7656,19542,19625,19923,20005,20324,20482,20557,20631,20703,20879,20946,21063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b9676b1ec2d82f1cef53d671c6d0afde\\transformed\\material-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "15820", "endColumns": "87", "endOffsets": "15903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9d7219de08abf79892e9add9bb5b00cd\\transformed\\material-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1041,1104,1194,1263,1323,1414,1477,1541,1600,1667,1729,1784,1907,1965,2026,2081,2153,2290,2371,2451,2548,2629,2711,2841,2915,2989,3121,3207,3284,3335,3389,3455,3526,3603,3674,3753,3826,3900,3970,4044,4145,4231,4305,4394,4486,4560,4633,4722,4773,4853,4920,5003,5087,5149,5213,5276,5345,5439,5540,5633,5731,5786,5844,5922,6008,6085", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "254,329,406,488,581,668,765,894,978,1036,1099,1189,1258,1318,1409,1472,1536,1595,1662,1724,1779,1902,1960,2021,2076,2148,2285,2366,2446,2543,2624,2706,2836,2910,2984,3116,3202,3279,3330,3384,3450,3521,3598,3669,3748,3821,3895,3965,4039,4140,4226,4300,4389,4481,4555,4628,4717,4768,4848,4915,4998,5082,5144,5208,5271,5340,5434,5535,5628,5726,5781,5839,5917,6003,6080,6154"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3078,3153,3230,3312,3405,4216,4313,4442,7196,7254,7317,7407,7661,14853,14944,15007,15071,15130,15197,15259,15314,15437,15495,15556,15611,15683,15908,15989,16069,16166,16247,16329,16459,16533,16607,16739,16825,16902,16953,17007,17073,17144,17221,17292,17371,17444,17518,17588,17662,17763,17849,17923,18012,18104,18178,18251,18340,18391,18471,18538,18621,18705,18767,18831,18894,18963,19057,19158,19251,19349,19404,19630,20092,20178,20329", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "304,3148,3225,3307,3400,3487,4308,4437,4521,7249,7312,7402,7471,7716,14939,15002,15066,15125,15192,15254,15309,15432,15490,15551,15606,15678,15815,15984,16064,16161,16242,16324,16454,16528,16602,16734,16820,16897,16948,17002,17068,17139,17216,17287,17366,17439,17513,17583,17657,17758,17844,17918,18007,18099,18173,18246,18335,18386,18466,18533,18616,18700,18762,18826,18889,18958,19052,19153,19246,19344,19399,19457,19703,20173,20250,20398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b48892203169544dad7e4ce4c2aaf9a0\\transformed\\material3-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,280,394,512,608,704,818,957,1073,1208,1293,1396,1488,1585,1699,1822,1930,2063,2194,2318,2485,2607,2720,2836,2953,3046,3144,3265,3397,3504,3607,3712,3843,3979,4085,4195,4275,4368,4465,4586,4693,4802,4888,4972,5060,5159,5259,5341,5440,5524,5625,5726,5823,5923,6010,6114,6214,6317,6437,6519,6654,6777,6881,6979,7082", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,123,166,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,106,108,85,83,87,98,99,81,98,83,100,100,96,99,86,103,99,102,119,81,134,122,103,97,102,104", "endOffsets": "165,275,389,507,603,699,813,952,1068,1203,1288,1391,1483,1580,1694,1817,1925,2058,2189,2313,2480,2602,2715,2831,2948,3041,3139,3260,3392,3499,3602,3707,3838,3974,4080,4190,4270,4363,4460,4581,4688,4797,4883,4967,5055,5154,5254,5336,5435,5519,5620,5721,5818,5918,6005,6109,6209,6312,6432,6514,6649,6772,6876,6974,7077,7182"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7721,7836,7946,8060,8178,8274,8370,8484,8623,8739,8874,8959,9062,9154,9251,9365,9488,9596,9729,9860,9984,10151,10273,10386,10502,10619,10712,10810,10931,11063,11170,11273,11378,11509,11645,11751,11861,11941,12034,12131,12252,12359,12468,12554,12638,12726,12825,12925,13007,13106,13190,13291,13392,13489,13589,13676,13780,13880,13983,14103,14185,14320,14443,14547,14645,14748", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,123,166,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,106,108,85,83,87,98,99,81,98,83,100,100,96,99,86,103,99,102,119,81,134,122,103,97,102,104", "endOffsets": "7831,7941,8055,8173,8269,8365,8479,8618,8734,8869,8954,9057,9149,9246,9360,9483,9591,9724,9855,9979,10146,10268,10381,10497,10614,10707,10805,10926,11058,11165,11268,11373,11504,11640,11746,11856,11936,12029,12126,12247,12354,12463,12549,12633,12721,12820,12920,13002,13101,13185,13286,13387,13484,13584,13671,13775,13875,13978,14098,14180,14315,14438,14542,14640,14743,14848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\bd9e012c025b7c80daa2bbfa6a34ffed\\transformed\\foundation-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,223", "endColumns": "78,88,88", "endOffsets": "129,218,307"}, "to": {"startLines": "33,224,225", "startColumns": "4,4,4", "startOffsets": "2999,21068,21157", "endColumns": "78,88,88", "endOffsets": "3073,21152,21241"}}]}]}