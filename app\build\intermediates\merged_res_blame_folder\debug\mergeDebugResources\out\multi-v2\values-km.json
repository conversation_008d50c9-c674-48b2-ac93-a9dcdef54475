{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c0ee106e096d6fe0bd1735cc2a0a4c89\\transformed\\quickie-bundled-1.10.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,117", "endColumns": "61,63", "endOffsets": "112,176"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "19974,20036", "endColumns": "61,63", "endOffsets": "20031,20095"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\bd9e012c025b7c80daa2bbfa6a34ffed\\transformed\\foundation-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,233", "endColumns": "86,90,91", "endOffsets": "137,228,320"}, "to": {"startLines": "33,224,225", "startColumns": "4,4,4", "startOffsets": "3007,21335,21426", "endColumns": "86,90,91", "endOffsets": "3089,21421,21513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9436b4ebc37cac3d97710224e74d8985\\transformed\\play-services-base-18.5.0\\res\\values-km\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4664,4766,4921,5042,5147,5309,5433,5554,5812,5970,6087,6258,6383,6528,6686,6750,6808", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "4761,4916,5037,5142,5304,5428,5549,5658,5965,6082,6253,6378,6523,6681,6745,6803,6880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b9676b1ec2d82f1cef53d671c6d0afde\\transformed\\material-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "15911", "endColumns": "91", "endOffsets": "15998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f58a4462c0f661bc016516b04b05ffbf\\transformed\\core-1.16.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "39,40,41,42,43,44,45,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3486,3581,3684,3782,3882,3983,4095,20966", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3576,3679,3777,3877,3978,4090,4202,21062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\ec01753cc085eccabdf318240ae84988\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,278,358,462,560,648,732,815,900,987,1067,1152,1228,1316,1390,1462,1533,1617,1683", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,87,73,71,70,83,65,117", "endOffsets": "273,353,457,555,643,727,810,895,982,1062,1147,1223,1311,1385,1457,1528,1612,1678,1796"}, "to": {"startLines": "49,50,69,70,71,76,77,204,205,209,210,214,216,217,218,219,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4499,4584,6885,6989,7087,7470,7554,19719,19804,20100,20180,20509,20661,20749,20823,20895,21067,21151,21217", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,87,73,71,70,83,65,117", "endOffsets": "4579,4659,6984,7082,7170,7549,7632,19799,19886,20175,20260,20580,20744,20818,20890,20961,21146,21212,21330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b48892203169544dad7e4ce4c2aaf9a0\\transformed\\material3-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,398,514,616,722,845,989,1117,1269,1360,1460,1560,1670,1794,1919,2024,2150,2276,2404,2566,2688,2802,2915,3038,3139,3239,3365,3504,3608,3713,3825,3950,4078,4195,4303,4379,4476,4572,4680,4785,4888,4976,5064,5156,5257,5353,5433,5537,5621,5721,5823,5919,6028,6115,6220,6318,6429,6546,6626,6767,6901,7008,7108,7210", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,104,102,87,87,91,100,95,79,103,83,99,101,95,108,86,104,97,110,116,79,140,133,106,99,101,103", "endOffsets": "168,287,393,509,611,717,840,984,1112,1264,1355,1455,1555,1665,1789,1914,2019,2145,2271,2399,2561,2683,2797,2910,3033,3134,3234,3360,3499,3603,3708,3820,3945,4073,4190,4298,4374,4471,4567,4675,4780,4883,4971,5059,5151,5252,5348,5428,5532,5616,5716,5818,5914,6023,6110,6215,6313,6424,6541,6621,6762,6896,7003,7103,7205,7309"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7699,7817,7936,8042,8158,8260,8366,8489,8633,8761,8913,9004,9104,9204,9314,9438,9563,9668,9794,9920,10048,10210,10332,10446,10559,10682,10783,10883,11009,11148,11252,11357,11469,11594,11722,11839,11947,12023,12120,12216,12324,12429,12532,12620,12708,12800,12901,12997,13077,13181,13265,13365,13467,13563,13672,13759,13864,13962,14073,14190,14270,14411,14545,14652,14752,14854", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,104,102,87,87,91,100,95,79,103,83,99,101,95,108,86,104,97,110,116,79,140,133,106,99,101,103", "endOffsets": "7812,7931,8037,8153,8255,8361,8484,8628,8756,8908,8999,9099,9199,9309,9433,9558,9663,9789,9915,10043,10205,10327,10441,10554,10677,10778,10878,11004,11143,11247,11352,11464,11589,11717,11834,11942,12018,12115,12211,12319,12424,12527,12615,12703,12795,12896,12992,13072,13176,13260,13360,13462,13558,13667,13754,13859,13957,14068,14185,14265,14406,14540,14647,14747,14849,14953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9d7219de08abf79892e9add9bb5b00cd\\transformed\\material-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1019,1084,1178,1248,1310,1397,1460,1525,1584,1649,1710,1767,1886,1944,2005,2062,2133,2263,2349,2425,2510,2592,2670,2808,2883,2954,3104,3201,3279,3334,3390,3456,3536,3626,3697,3782,3861,3938,4008,4083,4195,4283,4356,4456,4555,4629,4705,4812,4866,4956,5029,5120,5216,5278,5342,5405,5476,5575,5673,5765,5861,5919,5979,6062,6144,6222", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "264,342,418,498,577,656,756,868,948,1014,1079,1173,1243,1305,1392,1455,1520,1579,1644,1705,1762,1881,1939,2000,2057,2128,2258,2344,2420,2505,2587,2665,2803,2878,2949,3099,3196,3274,3329,3385,3451,3531,3621,3692,3777,3856,3933,4003,4078,4190,4278,4351,4451,4550,4624,4700,4807,4861,4951,5024,5115,5211,5273,5337,5400,5471,5570,5668,5760,5856,5914,5974,6057,6139,6217,6293"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3094,3172,3248,3328,3407,4207,4307,4419,7175,7241,7306,7400,7637,14958,15045,15108,15173,15232,15297,15358,15415,15534,15592,15653,15710,15781,16003,16089,16165,16250,16332,16410,16548,16623,16694,16844,16941,17019,17074,17130,17196,17276,17366,17437,17522,17601,17678,17748,17823,17935,18023,18096,18196,18295,18369,18445,18552,18606,18696,18769,18860,18956,19018,19082,19145,19216,19315,19413,19505,19601,19659,19891,20349,20431,20585", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "314,3167,3243,3323,3402,3481,4302,4414,4494,7236,7301,7395,7465,7694,15040,15103,15168,15227,15292,15353,15410,15529,15587,15648,15705,15776,15906,16084,16160,16245,16327,16405,16543,16618,16689,16839,16936,17014,17069,17125,17191,17271,17361,17432,17517,17596,17673,17743,17818,17930,18018,18091,18191,18290,18364,18440,18547,18601,18691,18764,18855,18951,19013,19077,19140,19211,19310,19408,19500,19596,19654,19714,19969,20426,20504,20656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\96abb1e1502d74c75d73c35e14f81e22\\transformed\\play-services-basement-18.5.0\\res\\values-km\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5663", "endColumns": "148", "endOffsets": "5807"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\757e326fd7991b30514ac12e75a7701a\\transformed\\appcompat-1.7.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,20265", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,20344"}}]}]}