package me.rerere.rikkahub.`data`.db.dao

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.coroutines.createFlow
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import javax.`annotation`.processing.Generated
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.Flow
import me.rerere.ai.core.TokenUsage
import me.rerere.rikkahub.`data`.db.TokenUsageConverter
import me.rerere.rikkahub.`data`.db.entity.ConversationEntity

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class ConversationDAO_Impl(
  __db: RoomDatabase,
) : ConversationDAO {
  private val __db: RoomDatabase

  private val __insertAdapterOfConversationEntity: EntityInsertAdapter<ConversationEntity>

  private val __deleteAdapterOfConversationEntity: EntityDeleteOrUpdateAdapter<ConversationEntity>

  private val __updateAdapterOfConversationEntity: EntityDeleteOrUpdateAdapter<ConversationEntity>
  init {
    this.__db = __db
    this.__insertAdapterOfConversationEntity = object : EntityInsertAdapter<ConversationEntity>() {
      protected override fun createQuery(): String =
          "INSERT OR ABORT INTO `ConversationEntity` (`id`,`assistant_id`,`title`,`messages`,`usage`,`create_at`,`update_at`) VALUES (?,?,?,?,?,?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: ConversationEntity) {
        statement.bindText(1, entity.id)
        statement.bindText(2, entity.assistantId)
        statement.bindText(3, entity.title)
        statement.bindText(4, entity.messages)
        val _tmpTokenUsage: TokenUsage? = entity.tokenUsage
        val _tmp: String = TokenUsageConverter.fromTokenUsage(_tmpTokenUsage)
        if (_tmp == null) {
          statement.bindNull(5)
        } else {
          statement.bindText(5, _tmp)
        }
        statement.bindLong(6, entity.createAt)
        statement.bindLong(7, entity.updateAt)
      }
    }
    this.__deleteAdapterOfConversationEntity = object :
        EntityDeleteOrUpdateAdapter<ConversationEntity>() {
      protected override fun createQuery(): String =
          "DELETE FROM `ConversationEntity` WHERE `id` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: ConversationEntity) {
        statement.bindText(1, entity.id)
      }
    }
    this.__updateAdapterOfConversationEntity = object :
        EntityDeleteOrUpdateAdapter<ConversationEntity>() {
      protected override fun createQuery(): String =
          "UPDATE OR ABORT `ConversationEntity` SET `id` = ?,`assistant_id` = ?,`title` = ?,`messages` = ?,`usage` = ?,`create_at` = ?,`update_at` = ? WHERE `id` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: ConversationEntity) {
        statement.bindText(1, entity.id)
        statement.bindText(2, entity.assistantId)
        statement.bindText(3, entity.title)
        statement.bindText(4, entity.messages)
        val _tmpTokenUsage: TokenUsage? = entity.tokenUsage
        val _tmp: String = TokenUsageConverter.fromTokenUsage(_tmpTokenUsage)
        if (_tmp == null) {
          statement.bindNull(5)
        } else {
          statement.bindText(5, _tmp)
        }
        statement.bindLong(6, entity.createAt)
        statement.bindLong(7, entity.updateAt)
        statement.bindText(8, entity.id)
      }
    }
  }

  public override suspend fun insert(conversation: ConversationEntity): Unit =
      performSuspending(__db, false, true) { _connection ->
    __insertAdapterOfConversationEntity.insert(_connection, conversation)
  }

  public override suspend fun delete(conversation: ConversationEntity): Unit =
      performSuspending(__db, false, true) { _connection ->
    __deleteAdapterOfConversationEntity.handle(_connection, conversation)
  }

  public override suspend fun update(conversation: ConversationEntity): Unit =
      performSuspending(__db, false, true) { _connection ->
    __updateAdapterOfConversationEntity.handle(_connection, conversation)
  }

  public override fun getAll(): Flow<List<ConversationEntity>> {
    val _sql: String = "SELECT * FROM conversationentity ORDER BY update_at DESC"
    return createFlow(__db, false, arrayOf("conversationentity")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfAssistantId: Int = getColumnIndexOrThrow(_stmt, "assistant_id")
        val _columnIndexOfTitle: Int = getColumnIndexOrThrow(_stmt, "title")
        val _columnIndexOfMessages: Int = getColumnIndexOrThrow(_stmt, "messages")
        val _columnIndexOfTokenUsage: Int = getColumnIndexOrThrow(_stmt, "usage")
        val _columnIndexOfCreateAt: Int = getColumnIndexOrThrow(_stmt, "create_at")
        val _columnIndexOfUpdateAt: Int = getColumnIndexOrThrow(_stmt, "update_at")
        val _result: MutableList<ConversationEntity> = mutableListOf()
        while (_stmt.step()) {
          val _item: ConversationEntity
          val _tmpId: String
          _tmpId = _stmt.getText(_columnIndexOfId)
          val _tmpAssistantId: String
          _tmpAssistantId = _stmt.getText(_columnIndexOfAssistantId)
          val _tmpTitle: String
          _tmpTitle = _stmt.getText(_columnIndexOfTitle)
          val _tmpMessages: String
          _tmpMessages = _stmt.getText(_columnIndexOfMessages)
          val _tmpTokenUsage: TokenUsage?
          val _tmp: String?
          if (_stmt.isNull(_columnIndexOfTokenUsage)) {
            _tmp = null
          } else {
            _tmp = _stmt.getText(_columnIndexOfTokenUsage)
          }
          if (_tmp == null) {
            _tmpTokenUsage = null
          } else {
            _tmpTokenUsage = TokenUsageConverter.toTokenUsage(_tmp)
          }
          val _tmpCreateAt: Long
          _tmpCreateAt = _stmt.getLong(_columnIndexOfCreateAt)
          val _tmpUpdateAt: Long
          _tmpUpdateAt = _stmt.getLong(_columnIndexOfUpdateAt)
          _item =
              ConversationEntity(_tmpId,_tmpAssistantId,_tmpTitle,_tmpMessages,_tmpTokenUsage,_tmpCreateAt,_tmpUpdateAt)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getConversationsOfAssistant(assistantId: String):
      Flow<List<ConversationEntity>> {
    val _sql: String =
        "SELECT * FROM conversationentity WHERE assistant_id = ? ORDER BY update_at DESC"
    return createFlow(__db, false, arrayOf("conversationentity")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, assistantId)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfAssistantId: Int = getColumnIndexOrThrow(_stmt, "assistant_id")
        val _columnIndexOfTitle: Int = getColumnIndexOrThrow(_stmt, "title")
        val _columnIndexOfMessages: Int = getColumnIndexOrThrow(_stmt, "messages")
        val _columnIndexOfTokenUsage: Int = getColumnIndexOrThrow(_stmt, "usage")
        val _columnIndexOfCreateAt: Int = getColumnIndexOrThrow(_stmt, "create_at")
        val _columnIndexOfUpdateAt: Int = getColumnIndexOrThrow(_stmt, "update_at")
        val _result: MutableList<ConversationEntity> = mutableListOf()
        while (_stmt.step()) {
          val _item: ConversationEntity
          val _tmpId: String
          _tmpId = _stmt.getText(_columnIndexOfId)
          val _tmpAssistantId: String
          _tmpAssistantId = _stmt.getText(_columnIndexOfAssistantId)
          val _tmpTitle: String
          _tmpTitle = _stmt.getText(_columnIndexOfTitle)
          val _tmpMessages: String
          _tmpMessages = _stmt.getText(_columnIndexOfMessages)
          val _tmpTokenUsage: TokenUsage?
          val _tmp: String?
          if (_stmt.isNull(_columnIndexOfTokenUsage)) {
            _tmp = null
          } else {
            _tmp = _stmt.getText(_columnIndexOfTokenUsage)
          }
          if (_tmp == null) {
            _tmpTokenUsage = null
          } else {
            _tmpTokenUsage = TokenUsageConverter.toTokenUsage(_tmp)
          }
          val _tmpCreateAt: Long
          _tmpCreateAt = _stmt.getLong(_columnIndexOfCreateAt)
          val _tmpUpdateAt: Long
          _tmpUpdateAt = _stmt.getLong(_columnIndexOfUpdateAt)
          _item =
              ConversationEntity(_tmpId,_tmpAssistantId,_tmpTitle,_tmpMessages,_tmpTokenUsage,_tmpCreateAt,_tmpUpdateAt)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun searchConversations(searchText: String): Flow<List<ConversationEntity>> {
    val _sql: String =
        "SELECT * FROM conversationentity WHERE title LIKE '%' || ? || '%' ORDER BY update_at DESC"
    return createFlow(__db, false, arrayOf("conversationentity")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, searchText)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfAssistantId: Int = getColumnIndexOrThrow(_stmt, "assistant_id")
        val _columnIndexOfTitle: Int = getColumnIndexOrThrow(_stmt, "title")
        val _columnIndexOfMessages: Int = getColumnIndexOrThrow(_stmt, "messages")
        val _columnIndexOfTokenUsage: Int = getColumnIndexOrThrow(_stmt, "usage")
        val _columnIndexOfCreateAt: Int = getColumnIndexOrThrow(_stmt, "create_at")
        val _columnIndexOfUpdateAt: Int = getColumnIndexOrThrow(_stmt, "update_at")
        val _result: MutableList<ConversationEntity> = mutableListOf()
        while (_stmt.step()) {
          val _item: ConversationEntity
          val _tmpId: String
          _tmpId = _stmt.getText(_columnIndexOfId)
          val _tmpAssistantId: String
          _tmpAssistantId = _stmt.getText(_columnIndexOfAssistantId)
          val _tmpTitle: String
          _tmpTitle = _stmt.getText(_columnIndexOfTitle)
          val _tmpMessages: String
          _tmpMessages = _stmt.getText(_columnIndexOfMessages)
          val _tmpTokenUsage: TokenUsage?
          val _tmp: String?
          if (_stmt.isNull(_columnIndexOfTokenUsage)) {
            _tmp = null
          } else {
            _tmp = _stmt.getText(_columnIndexOfTokenUsage)
          }
          if (_tmp == null) {
            _tmpTokenUsage = null
          } else {
            _tmpTokenUsage = TokenUsageConverter.toTokenUsage(_tmp)
          }
          val _tmpCreateAt: Long
          _tmpCreateAt = _stmt.getLong(_columnIndexOfCreateAt)
          val _tmpUpdateAt: Long
          _tmpUpdateAt = _stmt.getLong(_columnIndexOfUpdateAt)
          _item =
              ConversationEntity(_tmpId,_tmpAssistantId,_tmpTitle,_tmpMessages,_tmpTokenUsage,_tmpCreateAt,_tmpUpdateAt)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getConversationFlowById(id: String): Flow<ConversationEntity?> {
    val _sql: String = "SELECT * FROM conversationentity WHERE id = ?"
    return createFlow(__db, false, arrayOf("conversationentity")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, id)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfAssistantId: Int = getColumnIndexOrThrow(_stmt, "assistant_id")
        val _columnIndexOfTitle: Int = getColumnIndexOrThrow(_stmt, "title")
        val _columnIndexOfMessages: Int = getColumnIndexOrThrow(_stmt, "messages")
        val _columnIndexOfTokenUsage: Int = getColumnIndexOrThrow(_stmt, "usage")
        val _columnIndexOfCreateAt: Int = getColumnIndexOrThrow(_stmt, "create_at")
        val _columnIndexOfUpdateAt: Int = getColumnIndexOrThrow(_stmt, "update_at")
        val _result: ConversationEntity?
        if (_stmt.step()) {
          val _tmpId: String
          _tmpId = _stmt.getText(_columnIndexOfId)
          val _tmpAssistantId: String
          _tmpAssistantId = _stmt.getText(_columnIndexOfAssistantId)
          val _tmpTitle: String
          _tmpTitle = _stmt.getText(_columnIndexOfTitle)
          val _tmpMessages: String
          _tmpMessages = _stmt.getText(_columnIndexOfMessages)
          val _tmpTokenUsage: TokenUsage?
          val _tmp: String?
          if (_stmt.isNull(_columnIndexOfTokenUsage)) {
            _tmp = null
          } else {
            _tmp = _stmt.getText(_columnIndexOfTokenUsage)
          }
          if (_tmp == null) {
            _tmpTokenUsage = null
          } else {
            _tmpTokenUsage = TokenUsageConverter.toTokenUsage(_tmp)
          }
          val _tmpCreateAt: Long
          _tmpCreateAt = _stmt.getLong(_columnIndexOfCreateAt)
          val _tmpUpdateAt: Long
          _tmpUpdateAt = _stmt.getLong(_columnIndexOfUpdateAt)
          _result =
              ConversationEntity(_tmpId,_tmpAssistantId,_tmpTitle,_tmpMessages,_tmpTokenUsage,_tmpCreateAt,_tmpUpdateAt)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getConversationById(id: String): ConversationEntity? {
    val _sql: String = "SELECT * FROM conversationentity WHERE id = ?"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, id)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfAssistantId: Int = getColumnIndexOrThrow(_stmt, "assistant_id")
        val _columnIndexOfTitle: Int = getColumnIndexOrThrow(_stmt, "title")
        val _columnIndexOfMessages: Int = getColumnIndexOrThrow(_stmt, "messages")
        val _columnIndexOfTokenUsage: Int = getColumnIndexOrThrow(_stmt, "usage")
        val _columnIndexOfCreateAt: Int = getColumnIndexOrThrow(_stmt, "create_at")
        val _columnIndexOfUpdateAt: Int = getColumnIndexOrThrow(_stmt, "update_at")
        val _result: ConversationEntity?
        if (_stmt.step()) {
          val _tmpId: String
          _tmpId = _stmt.getText(_columnIndexOfId)
          val _tmpAssistantId: String
          _tmpAssistantId = _stmt.getText(_columnIndexOfAssistantId)
          val _tmpTitle: String
          _tmpTitle = _stmt.getText(_columnIndexOfTitle)
          val _tmpMessages: String
          _tmpMessages = _stmt.getText(_columnIndexOfMessages)
          val _tmpTokenUsage: TokenUsage?
          val _tmp: String?
          if (_stmt.isNull(_columnIndexOfTokenUsage)) {
            _tmp = null
          } else {
            _tmp = _stmt.getText(_columnIndexOfTokenUsage)
          }
          if (_tmp == null) {
            _tmpTokenUsage = null
          } else {
            _tmpTokenUsage = TokenUsageConverter.toTokenUsage(_tmp)
          }
          val _tmpCreateAt: Long
          _tmpCreateAt = _stmt.getLong(_columnIndexOfCreateAt)
          val _tmpUpdateAt: Long
          _tmpUpdateAt = _stmt.getLong(_columnIndexOfUpdateAt)
          _result =
              ConversationEntity(_tmpId,_tmpAssistantId,_tmpTitle,_tmpMessages,_tmpTokenUsage,_tmpCreateAt,_tmpUpdateAt)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteAll() {
    val _sql: String = "DELETE FROM conversationentity"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
