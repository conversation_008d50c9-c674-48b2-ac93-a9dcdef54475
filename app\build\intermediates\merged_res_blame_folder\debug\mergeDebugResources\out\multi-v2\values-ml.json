{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b9676b1ec2d82f1cef53d671c6d0afde\\transformed\\material-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "95", "endOffsets": "146"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "16523", "endColumns": "95", "endOffsets": "16614"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\96abb1e1502d74c75d73c35e14f81e22\\transformed\\play-services-basement-18.5.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5885", "endColumns": "159", "endOffsets": "6040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9436b4ebc37cac3d97710224e74d8985\\transformed\\play-services-base-18.5.0\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4827,4946,5125,5264,5385,5549,5674,5779,6045,6227,6343,6517,6653,6802,6963,7027,7096", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "4941,5120,5259,5380,5544,5669,5774,5880,6222,6338,6512,6648,6797,6958,7022,7091,7183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f58a4462c0f661bc016516b04b05ffbf\\transformed\\core-1.16.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "39,40,41,42,43,44,45,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3714,3817,3919,4023,4126,4227,21757", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3709,3812,3914,4018,4121,4222,4344,21853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\ec01753cc085eccabdf318240ae84988\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,288,375,474,578,668,754,855,942,1030,1116,1203,1281,1373,1450,1524,1597,1673,1740", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,91,76,73,72,75,66,118", "endOffsets": "283,370,469,573,663,749,850,937,1025,1111,1198,1276,1368,1445,1519,1592,1668,1735,1854"}, "to": {"startLines": "49,50,69,70,71,76,77,204,205,209,210,214,216,217,218,219,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4645,4740,7188,7287,7391,7773,7859,20461,20548,20859,20945,21287,21441,21533,21610,21684,21858,21934,22001", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,91,76,73,72,75,66,118", "endOffsets": "4735,4822,7282,7386,7476,7854,7955,20543,20631,20940,21027,21360,21528,21605,21679,21752,21929,21996,22115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b48892203169544dad7e4ce4c2aaf9a0\\transformed\\material3-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4832,4937,5045,5132,5229,5325,5433,5529,5609,5716,5804,5902,6015,6110,6221,6311,6426,6528,6641,6773,6853,6997,7127,7234,7331,7441", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,104,107,86,96,95,107,95,79,106,87,97,112,94,110,89,114,101,112,131,79,143,129,106,96,109,108", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4827,4932,5040,5127,5224,5320,5428,5524,5604,5711,5799,5897,6010,6105,6216,6306,6421,6523,6636,6768,6848,6992,7122,7229,7326,7436,7545"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8020,8140,8266,8392,8515,8615,8709,8820,8972,9090,9247,9332,9437,9537,9639,9762,9895,10005,10141,10283,10414,10618,10752,10876,11006,11140,11241,11339,11457,11588,11687,11789,11902,12040,12186,12300,12409,12485,12583,12683,12797,12902,13010,13097,13194,13290,13398,13494,13574,13681,13769,13867,13980,14075,14186,14276,14391,14493,14606,14738,14818,14962,15092,15199,15296,15406", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,104,107,86,96,95,107,95,79,106,87,97,112,94,110,89,114,101,112,131,79,143,129,106,96,109,108", "endOffsets": "8135,8261,8387,8510,8610,8704,8815,8967,9085,9242,9327,9432,9532,9634,9757,9890,10000,10136,10278,10409,10613,10747,10871,11001,11135,11236,11334,11452,11583,11682,11784,11897,12035,12181,12295,12404,12480,12578,12678,12792,12897,13005,13092,13189,13285,13393,13489,13569,13676,13764,13862,13975,14070,14181,14271,14386,14488,14601,14733,14813,14957,15087,15194,15291,15401,15510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\bd9e012c025b7c80daa2bbfa6a34ffed\\transformed\\foundation-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,230", "endColumns": "86,87,91", "endOffsets": "137,225,317"}, "to": {"startLines": "33,224,225", "startColumns": "4,4,4", "startOffsets": "3070,22120,22208", "endColumns": "86,87,91", "endOffsets": "3152,22203,22295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9d7219de08abf79892e9add9bb5b00cd\\transformed\\material-1.12.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,627,726,820,930,1022,1084,1149,1248,1314,1374,1476,1538,1614,1672,1750,1815,1869,1986,2050,2114,2168,2248,2382,2468,2555,2658,2754,2843,2979,3064,3152,3304,3399,3482,3540,3592,3658,3737,3819,3890,3977,4053,4130,4207,4278,4388,4495,4575,4672,4772,4846,4927,5032,5090,5178,5245,5336,5428,5490,5554,5617,5686,5789,5896,6001,6106,6168,6224,6308,6402,6480", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "266,346,429,516,622,721,815,925,1017,1079,1144,1243,1309,1369,1471,1533,1609,1667,1745,1810,1864,1981,2045,2109,2163,2243,2377,2463,2550,2653,2749,2838,2974,3059,3147,3299,3394,3477,3535,3587,3653,3732,3814,3885,3972,4048,4125,4202,4273,4383,4490,4570,4667,4767,4841,4922,5027,5085,5173,5240,5331,5423,5485,5549,5612,5681,5784,5891,5996,6101,6163,6219,6303,6397,6475,6551"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3237,3320,3407,3513,4349,4443,4553,7481,7543,7608,7707,7960,15515,15617,15679,15755,15813,15891,15956,16010,16127,16191,16255,16309,16389,16619,16705,16792,16895,16991,17080,17216,17301,17389,17541,17636,17719,17777,17829,17895,17974,18056,18127,18214,18290,18367,18444,18515,18625,18732,18812,18909,19009,19083,19164,19269,19327,19415,19482,19573,19665,19727,19791,19854,19923,20026,20133,20238,20343,20405,20636,21115,21209,21365", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,75,78,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,206,212,213,215", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "316,3232,3315,3402,3508,3607,4438,4548,4640,7538,7603,7702,7768,8015,15612,15674,15750,15808,15886,15951,16005,16122,16186,16250,16304,16384,16518,16700,16787,16890,16986,17075,17211,17296,17384,17536,17631,17714,17772,17824,17890,17969,18051,18122,18209,18285,18362,18439,18510,18620,18727,18807,18904,19004,19078,19159,19264,19322,19410,19477,19568,19660,19722,19786,19849,19918,20021,20128,20233,20338,20400,20456,20715,21204,21282,21436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\757e326fd7991b30514ac12e75a7701a\\transformed\\appcompat-1.7.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,428,534,645,736,841,963,1041,1116,1207,1300,1401,1495,1595,1689,1784,1883,1974,2065,2147,2256,2360,2459,2571,2683,2804,2969,21032", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "423,529,640,731,836,958,1036,1111,1202,1295,1396,1490,1590,1684,1779,1878,1969,2060,2142,2251,2355,2454,2566,2678,2799,2964,3065,21110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c0ee106e096d6fe0bd1735cc2a0a4c89\\transformed\\quickie-bundled-1.10.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,120", "endColumns": "64,73", "endOffsets": "115,189"}, "to": {"startLines": "207,208", "startColumns": "4,4", "startOffsets": "20720,20785", "endColumns": "64,73", "endOffsets": "20780,20854"}}]}]}