package me.rerere.rag.spliter;

/**
 * 基于正则表达式的文本分割器
 * @param pattern 用于分割文本的正则表达式模式
 * @param chunkSize 每个文本块的最大大小
 * @param chunkOverlap 相邻文本块之间的重叠大小
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0000\u0018\u00002\u00020\u0001B%\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0016\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\n2\u0006\u0010\u000b\u001a\u00020\u0003H\u0016J\u0014\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u000e0\rH\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lme/rerere/rag/spliter/RegexTextSplitter;", "Lme/rerere/rag/spliter/TextSplitter;", "pattern", "", "chunkSize", "", "chunkOverlap", "<init>", "(Ljava/lang/String;II)V", "split", "", "text", "getConfig", "", "", "rag_debug"})
public final class RegexTextSplitter implements me.rerere.rag.spliter.TextSplitter {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String pattern = null;
    private final int chunkSize = 0;
    private final int chunkOverlap = 0;
    
    public RegexTextSplitter(@org.jetbrains.annotations.NotNull()
    java.lang.String pattern, int chunkSize, int chunkOverlap) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.util.List<java.lang.String> split(@org.jetbrains.annotations.NotNull()
    java.lang.String text) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.util.Map<java.lang.String, java.lang.Object> getConfig() {
        return null;
    }
    
    public RegexTextSplitter() {
        super();
    }
}