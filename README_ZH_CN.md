<div align="center">
  <img src="docs/icon.png" alt="App 图标" width="100" />
  <h1>RikkaHub</h1>

一个原生Android LLM 聊天客户端，支持切换不同的供应商进行聊天 🤖💬

[English](README.md) | [繁體中文](README_ZH_TW.md) | 简体中文

点击链接加入群聊 👉 [【RikkaHub】](https://qm.qq.com/q/I8MSU0FkOu)

</div>

<div align="center">
  <img src="docs/img/chat.png" alt="Chat Interface" width="150" />
  <img src="docs/img/models.png" alt="Models Picker" width="150" />
  <img src="docs/img/providers.png" alt="Providers" width="150" />
  <img src="docs/img/assistants.png" alt="Assistants" width="150" />
</div>

## 🚀 下载

🔗 [点击前往官网下载](https://rikka-ai.com/)

## ✨ 功能特色

- 🎨 现代化安卓APP设计（Material You / 预测性返回）
- 🌙 暗色模式
- 🔄 多种类型的供应商支持，自定义 API / URL / 模型（目前支持 OpenAI、Google）
- 🖼️ 多模态输入支持
- 📝 Markdown 渲染（支持代码高亮、数学公式、表格、Mermaid）
- 🔍 搜索支持（Exa、Tavily、Zhipu）
- 🧩 Prompt 变量（模型名称、时间等）
- 🤳 二维码导出和导入提供商
- 🤖 智能体自定义
- 🧠 类ChatGPT记忆功能
- 📝 翻译页面
- 🌐 自定义HTTP请求头和请求体

## ✨ 贡献

本项目使用[Android Studio](https://developer.android.com/studio)开发，欢迎提交PR

技术栈文档:

- [Kotlin](https://kotlinlang.org/) (开发语言)
- [Koin](https://insert-koin.io/) (依赖注入)
- [Jetpack Compose](https://developer.android.com/jetpack/compose) (UI 框架)
- [DataStore](https://developer.android.com/topic/libraries/architecture/datastore?hl=zh-cn#preferences-datastore) (偏好数据存储)
- [Room](https://developer.android.com/training/data-storage/room) (数据库)
- [Coil](https://coil-kt.github.io/coil/) (图片加载)
- [Material You](https://m3.material.io/) (UI 设计)
- [Navigation Compose](https://developer.android.com/develop/ui/compose/navigation) (导航)
- [Okhttp](https://square.github.io/okhttp/) (HTTP 客户端)
- [kotlinx.serialization](https://github.com/Kotlin/kotlinx.serialization) (Json序列化)
- [compose-icons/lucide](https://composeicons.com/icon-libraries/lucide) (图标库)

> 你需要在 `app` 文件夹下添加 `google-services.json` 文件才能构建应用

## 💖 捐赠

如果你喜欢这个项目，可以请我喝杯咖啡 ☕

<div>
  <img src="docs/donate.png" alt="捐赠" width="200" />
</div>

## Star History

如果喜欢这个项目，可以给个Star ⭐

[![Star History Chart](https://api.star-history.com/svg?repos=re-ovo/rikkahub&type=Date)](https://star-history.com/#re-ovo/rikkahub&Date)

## 📄 许可证

[License](LICENSE)