package me.rerere.rag.extractor.impl;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0016\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0002H\u0016\u00a8\u0006\b"}, d2 = {"Lme/rerere/rag/extractor/impl/TextExtractor;", "Lme/rerere/rag/extractor/DataExtractor;", "", "<init>", "()V", "extract", "", "data", "rag_debug"})
public final class TextExtractor implements me.rerere.rag.extractor.DataExtractor<java.lang.String> {
    
    public TextExtractor() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.util.List<java.lang.String> extract(@org.jetbrains.annotations.NotNull()
    java.lang.String data) {
        return null;
    }
}