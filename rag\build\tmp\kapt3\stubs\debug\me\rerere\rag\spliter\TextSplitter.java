package me.rerere.rag.spliter;

/**
 * 文本分割器接口，用于将长文本分割成更小的片段
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010$\n\u0000\bf\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H&J\u0014\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0007H&\u00a8\u0006\b"}, d2 = {"Lme/rerere/rag/spliter/TextSplitter;", "", "split", "", "", "text", "getConfig", "", "rag_debug"})
public abstract interface TextSplitter {
    
    /**
     * 将文本分割成多个片段
     * @param text 要分割的文本
     * @return 分割后的文本片段列表
     */
    @org.jetbrains.annotations.NotNull()
    public abstract java.util.List<java.lang.String> split(@org.jetbrains.annotations.NotNull()
    java.lang.String text);
    
    /**
     * 获取分割器的配置信息
     * @return 配置信息Map
     */
    @org.jetbrains.annotations.NotNull()
    public abstract java.util.Map<java.lang.String, java.lang.Object> getConfig();
}