{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\e744a31c82164d30026af487ba5e43bf\\transformed\\uCrop-n-Edit-4.1.0-non-native\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,101,164,222,273,320", "endColumns": "45,62,57,50,46,47", "endOffsets": "96,159,217,268,315,363"}, "to": {"startLines": "129,130,131,132,133,134", "startColumns": "4,4,4,4,4,4", "startOffsets": "13177,13223,13286,13344,13395,13442", "endColumns": "45,62,57,50,46,47", "endOffsets": "13218,13281,13339,13390,13437,13485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f58a4462c0f661bc016516b04b05ffbf\\transformed\\core-1.16.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "30,31,32,33,34,35,36,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2934,3031,3133,3232,3332,3442,3552,12652", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "3026,3128,3227,3327,3437,3547,3667,12748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\ec01753cc085eccabdf318240ae84988\\transformed\\ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "197,292,378,475,574,660,743,840,931,1018,1103,1193,1269,1354,1430,1509,1584,1660,1727", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "287,373,470,569,655,738,835,926,1013,1098,1188,1264,1349,1425,1504,1579,1655,1722,1835"}, "to": {"startLines": "37,38,39,40,41,42,43,111,112,115,116,118,119,120,121,122,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3672,3767,3853,3950,4049,4135,4218,11698,11789,12000,12085,12261,12337,12422,12498,12577,12753,12829,12896", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "3762,3848,3945,4044,4130,4213,4310,11784,11871,12080,12170,12332,12417,12493,12572,12647,12824,12891,13004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\757e326fd7991b30514ac12e75a7701a\\transformed\\appcompat-1.7.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,12175", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,12256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b48892203169544dad7e4ce4c2aaf9a0\\transformed\\material3-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4673,4789,4902,4992,5081,5170,5271,5370,5450,5557,5641,5742,5848,5940,6039,6127,6239,6340,6444,6563,6643,6795,6935,7035,7127,7235", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,115,112,89,88,88,100,98,79,106,83,100,105,91,98,87,111,100,103,118,79,151,139,99,91,107,113", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4668,4784,4897,4987,5076,5165,5266,5365,5445,5552,5636,5737,5843,5935,6034,6122,6234,6335,6439,6558,6638,6790,6930,7030,7122,7230,7344"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4315,4434,4555,4671,4787,4889,4986,5100,5234,5352,5504,5588,5689,5784,5884,5999,6129,6235,6374,6510,6641,6807,6934,7054,7178,7298,7394,7491,7611,7727,7827,7938,8047,8187,8332,8442,8545,8631,8725,8817,8933,9049,9162,9252,9341,9430,9531,9630,9710,9817,9901,10002,10108,10200,10299,10387,10499,10600,10704,10823,10903,11055,11195,11295,11387,11495", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,115,112,89,88,88,100,98,79,106,83,100,105,91,98,87,111,100,103,118,79,151,139,99,91,107,113", "endOffsets": "4429,4550,4666,4782,4884,4981,5095,5229,5347,5499,5583,5684,5779,5879,5994,6124,6230,6369,6505,6636,6802,6929,7049,7173,7293,7389,7486,7606,7722,7822,7933,8042,8182,8327,8437,8540,8626,8720,8812,8928,9044,9157,9247,9336,9425,9526,9625,9705,9812,9896,9997,10103,10195,10294,10382,10494,10595,10699,10818,10898,11050,11190,11290,11382,11490,11604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c0ee106e096d6fe0bd1735cc2a0a4c89\\transformed\\quickie-bundled-1.10.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endColumns": "58,64", "endOffsets": "109,174"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11876,11935", "endColumns": "58,64", "endOffsets": "11930,11995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b9676b1ec2d82f1cef53d671c6d0afde\\transformed\\material-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "11609", "endColumns": "88", "endOffsets": "11693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\bd9e012c025b7c80daa2bbfa6a34ffed\\transformed\\foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,146,229", "endColumns": "90,82,84", "endOffsets": "141,224,309"}, "to": {"startLines": "29,127,128", "startColumns": "4,4,4", "startOffsets": "2843,13009,13092", "endColumns": "90,82,84", "endOffsets": "2929,13087,13172"}}]}]}