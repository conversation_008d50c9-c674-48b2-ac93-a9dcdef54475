{"logs": [{"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\be5cc04b6a4fee62d71666ba2f889b70\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "436,551,653,761,847,954,1073,1152,1228,1319,1412,1507,1601,1702,1795,1890,1985,2076,2167,2249,2358,2458,2557,2666,2778,2889,3052,20464", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "546,648,756,842,949,1068,1147,1223,1314,1407,1502,1596,1697,1790,1885,1980,2071,2162,2244,2353,2453,2552,2661,2773,2884,3047,3143,20542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\aaf9750c511786fa108e7a3c496402ad\\transformed\\play-services-basement-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5850", "endColumns": "139", "endOffsets": "5985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\835032a51b5d40afa0ab30aa91122c7e\\transformed\\quickie-bundled-1.10.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,115", "endColumns": "59,66", "endOffsets": "110,177"}, "to": {"startLines": "209,210", "startColumns": "4,4", "startOffsets": "20167,20227", "endColumns": "59,66", "endOffsets": "20222,20289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\303c2166503144bea901100ae0f040af\\transformed\\foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,226", "endColumns": "82,87,87", "endOffsets": "133,221,309"}, "to": {"startLines": "35,226,227", "startColumns": "4,4,4", "startOffsets": "3148,21543,21631", "endColumns": "82,87,87", "endOffsets": "3226,21626,21714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d642686678dc9e4dc645c444fc2595b8\\transformed\\core-1.16.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "41,42,43,44,45,46,47,222", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3659,3756,3858,3956,4055,4169,4274,21172", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3751,3853,3951,4050,4164,4269,4391,21268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4e69add8b1376897dea52ac0f7720f60\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189,284,369,478,583,660,737,830,920,1003,1086,1173,1245,1329,1405,1483,1559,1641,1709", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "279,364,473,578,655,732,825,915,998,1081,1168,1240,1324,1400,1478,1554,1636,1704,1824"}, "to": {"startLines": "51,52,71,72,73,78,79,206,207,211,212,216,218,219,220,221,223,224,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4680,4775,7112,7221,7326,7677,7754,19918,20008,20294,20377,20708,20858,20942,21018,21096,21273,21355,21423", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "4770,4855,7216,7321,7398,7749,7842,20003,20086,20372,20459,20775,20937,21013,21091,21167,21350,21418,21538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d2e6b12382a4dadf34d77e521366ccff\\transformed\\play-services-base-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4860,4964,5132,5254,5364,5515,5640,5751,5990,6161,6270,6445,6573,6732,6893,6962,7028", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "4959,5127,5249,5359,5510,5635,5746,5845,6156,6265,6440,6568,6727,6888,6957,7023,7107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8bf40832b048d1f4d2354372c996192d\\transformed\\material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4650,4758,4866,4953,5040,5128,5240,5339,5419,5522,5609,5704,5809,5900,6009,6097,6203,6304,6414,6532,6612,6749,6870,6973,7070,7180", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,107,107,86,86,87,111,98,79,102,86,94,104,90,108,87,105,100,109,117,79,136,120,102,96,109,111", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4645,4753,4861,4948,5035,5123,5235,5334,5414,5517,5604,5699,5804,5895,6004,6092,6198,6299,6409,6527,6607,6744,6865,6968,7065,7175,7287"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7908,8023,8140,8262,8377,8477,8576,8692,8830,8952,9094,9178,9277,9369,9465,9582,9706,9810,9950,10086,10230,10391,10523,10644,10769,10890,10983,11083,11203,11327,11426,11530,11636,11777,11924,12035,12134,12208,12303,12399,12503,12611,12719,12806,12893,12981,13093,13192,13272,13375,13462,13557,13662,13753,13862,13950,14056,14157,14267,14385,14465,14602,14723,14826,14923,15033", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,107,107,86,86,87,111,98,79,102,86,94,104,90,108,87,105,100,109,117,79,136,120,102,96,109,111", "endOffsets": "8018,8135,8257,8372,8472,8571,8687,8825,8947,9089,9173,9272,9364,9460,9577,9701,9805,9945,10081,10225,10386,10518,10639,10764,10885,10978,11078,11198,11322,11421,11525,11631,11772,11919,12030,12129,12203,12298,12394,12498,12606,12714,12801,12888,12976,13088,13187,13267,13370,13457,13552,13657,13748,13857,13945,14051,14152,14262,14380,14460,14597,14718,14821,14918,15028,15140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2f477ab85e7d6008688a1a0c4b191573\\transformed\\material-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "160", "startColumns": "4", "startOffsets": "16116", "endColumns": "87", "endOffsets": "16199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b26bc0a945bb328544f80db00c932160\\transformed\\material-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1163,1227,1308,1372,1433,1544,1608,1676,1730,1799,1861,1915,2026,2087,2149,2203,2275,2404,2493,2572,2667,2752,2834,2983,3065,3148,3285,3372,3449,3503,3554,3620,3691,3767,3838,3921,3998,4076,4154,4230,4338,4428,4501,4596,4693,4765,4839,4939,4991,5076,5142,5230,5320,5382,5446,5509,5580,5687,5799,5898,6005,6063,6118,6194,6278,6355", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1158,1222,1303,1367,1428,1539,1603,1671,1725,1794,1856,1910,2021,2082,2144,2198,2270,2399,2488,2567,2662,2747,2829,2978,3060,3143,3280,3367,3444,3498,3549,3615,3686,3762,3833,3916,3993,4071,4149,4225,4333,4423,4496,4591,4688,4760,4834,4934,4986,5071,5137,5225,5315,5377,5441,5504,5575,5682,5794,5893,6000,6058,6113,6189,6273,6350,6428"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,74,75,76,77,80,147,148,149,150,151,152,153,154,155,156,157,158,159,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,208,214,215,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3231,3306,3381,3460,3564,4396,4481,4598,7403,7468,7532,7613,7847,15145,15256,15320,15388,15442,15511,15573,15627,15738,15799,15861,15915,15987,16204,16293,16372,16467,16552,16634,16783,16865,16948,17085,17172,17249,17303,17354,17420,17491,17567,17638,17721,17798,17876,17954,18030,18138,18228,18301,18396,18493,18565,18639,18739,18791,18876,18942,19030,19120,19182,19246,19309,19380,19487,19599,19698,19805,19863,20091,20547,20631,20780", "endLines": "7,36,37,38,39,40,48,49,50,74,75,76,77,80,147,148,149,150,151,152,153,154,155,156,157,158,159,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,208,214,215,217", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "431,3301,3376,3455,3559,3654,4476,4593,4675,7463,7527,7608,7672,7903,15251,15315,15383,15437,15506,15568,15622,15733,15794,15856,15910,15982,16111,16288,16367,16462,16547,16629,16778,16860,16943,17080,17167,17244,17298,17349,17415,17486,17562,17633,17716,17793,17871,17949,18025,18133,18223,18296,18391,18488,18560,18634,18734,18786,18871,18937,19025,19115,19177,19241,19304,19375,19482,19594,19693,19800,19858,19913,20162,20626,20703,20853"}}]}]}