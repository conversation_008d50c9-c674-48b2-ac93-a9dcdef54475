<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\search\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\search\src\main\res"><file path="D:\FromTX3\MyCode\rikkahub\search\src\main\res\values\strings.xml" qualifiers=""><string name="bing_desc">Note that Bing search is crawler-based, which may be unstable and is not recommended.</string><string name="click_to_get_api_key">Click to get API Key</string></file><file path="D:\FromTX3\MyCode\rikkahub\search\src\main\res\values-zh\strings.xml" qualifiers="zh"><string name="bing_desc">注意Bing搜索目前基于爬虫，很容易被风控拦截，不推荐使用</string><string name="click_to_get_api_key">点击获取API Key</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\search\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\search\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\search\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\search\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>