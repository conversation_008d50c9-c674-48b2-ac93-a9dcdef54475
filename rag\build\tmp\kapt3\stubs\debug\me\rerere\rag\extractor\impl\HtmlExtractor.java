package me.rerere.rag.extractor.impl;

/**
 * 从HTML文本中提取纯文本内容
 * 使用 Jsoup 解析 HTML
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0000\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0016\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0002H\u0016J\u001e\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00020\rH\u0002\u00a8\u0006\u000e"}, d2 = {"Lme/rerere/rag/extractor/impl/HtmlExtractor;", "Lme/rerere/rag/extractor/DataExtractor;", "", "<init>", "()V", "extract", "", "data", "extractContent", "", "document", "Lorg/jsoup/nodes/Document;", "result", "", "rag_debug"})
public final class HtmlExtractor implements me.rerere.rag.extractor.DataExtractor<java.lang.String> {
    
    public HtmlExtractor() {
        super();
    }
    
    /**
     * 从HTML文本中提取纯文本内容
     * @param data HTML文本
     * @return 提取出的纯文本内容列表
     */
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.util.List<java.lang.String> extract(@org.jetbrains.annotations.NotNull()
    java.lang.String data) {
        return null;
    }
    
    /**
     * 提取文档中的主要内容
     */
    private final void extractContent(org.jsoup.nodes.Document document, java.util.List<java.lang.String> result) {
    }
}