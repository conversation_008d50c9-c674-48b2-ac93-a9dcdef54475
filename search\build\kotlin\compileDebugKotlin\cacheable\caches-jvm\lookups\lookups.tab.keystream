  RowScope "androidx.compose.foundation.layout  R +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  stringResource +androidx.compose.foundation.layout.RowScope  Text androidx.compose.material3  
TextButton androidx.compose.material3  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction1 !androidx.compose.runtime.internal  LocalUriHandler androidx.compose.ui.platform  
UriHandler androidx.compose.ui.platform  openUri 'androidx.compose.ui.platform.UriHandler  stringResource androidx.compose.ui.res  
URLEncoder java.net  encode java.net.URLEncoder  	Function0 kotlin  	Function1 kotlin  Lazy kotlin  Nothing kotlin  Pair kotlin  Result kotlin  Suppress kotlin  	Throwable kotlin  error kotlin  
getOrThrow kotlin  getValue kotlin  lazy kotlin  let kotlin  map kotlin  	onFailure kotlin  require kotlin  runCatching kotlin  to kotlin  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  	Companion 
kotlin.Result  
getOrThrow 
kotlin.Result  	onFailure 
kotlin.Result  success 
kotlin.Result  success kotlin.Result.Companion  let 
kotlin.String  plus 
kotlin.String  toMediaType 
kotlin.String  
toRequestBody 
kotlin.String  printStackTrace kotlin.Throwable  List kotlin.collections  Map kotlin.collections  	emptyList kotlin.collections  getValue kotlin.collections  
isNotEmpty kotlin.collections  map kotlin.collections  mapOf kotlin.collections  
isNotEmpty kotlin.collections.List  map kotlin.collections.List  SuspendFunction1 kotlin.coroutines  println 	kotlin.io  KClass kotlin.reflect  
KProperty1 kotlin.reflect  to kotlin.reflect.KClass  Sequence kotlin.sequences  map kotlin.sequences  
isNotEmpty kotlin.text  map kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  withContext kotlinx.coroutines  
JsonPrimitive !kotlinx.coroutines.CoroutineScope  Jsoup !kotlinx.coroutines.CoroutineScope  Request !kotlinx.coroutines.CoroutineScope  Result !kotlinx.coroutines.CoroutineScope  SearchResult !kotlinx.coroutines.CoroutineScope  SearchResultItem !kotlinx.coroutines.CoroutineScope  
URLEncoder !kotlinx.coroutines.CoroutineScope  buildJsonObject !kotlinx.coroutines.CoroutineScope  error !kotlinx.coroutines.CoroutineScope  
getOrThrow !kotlinx.coroutines.CoroutineScope  
httpClient !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  json !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  println !kotlinx.coroutines.CoroutineScope  require !kotlinx.coroutines.CoroutineScope  runCatching !kotlinx.coroutines.CoroutineScope  success !kotlinx.coroutines.CoroutineScope  toMediaType !kotlinx.coroutines.CoroutineScope  
toRequestBody !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  
SerialName kotlinx.serialization  Serializable kotlinx.serialization  Json kotlinx.serialization.json  JsonBuilder kotlinx.serialization.json  JsonElement kotlinx.serialization.json  JsonNull kotlinx.serialization.json  
JsonObject kotlinx.serialization.json  JsonObjectBuilder kotlinx.serialization.json  
JsonPrimitive kotlinx.serialization.json  buildJsonObject kotlinx.serialization.json  decodeFromString kotlinx.serialization.json.Json  encodeToString kotlinx.serialization.json.Json  
explicitNulls &kotlinx.serialization.json.JsonBuilder  ignoreUnknownKeys &kotlinx.serialization.json.JsonBuilder  toString %kotlinx.serialization.json.JsonObject  
JsonPrimitive ,kotlinx.serialization.json.JsonObjectBuilder  buildJsonObject ,kotlinx.serialization.json.JsonObjectBuilder  put ,kotlinx.serialization.json.JsonObjectBuilder  BingLocalOptions me.rerere.search  BingSearchService me.rerere.search  
Composable me.rerere.search  Dispatchers me.rerere.search  Double me.rerere.search  ExaContentsCost me.rerere.search  ExaCostDollars me.rerere.search  ExaData me.rerere.search  
ExaOptions me.rerere.search  	ExaResult me.rerere.search  
ExaSearchCost me.rerere.search  ExaSearchService me.rerere.search  Int me.rerere.search  Json me.rerere.search  
JsonPrimitive me.rerere.search  Jsoup me.rerere.search  List me.rerere.search  LocalUriHandler me.rerere.search  OkHttpClient me.rerere.search  R me.rerere.search  Request me.rerere.search  Result me.rerere.search  
ResultItem me.rerere.search  SearchCommonOptions me.rerere.search  SearchResponse me.rerere.search  SearchResult me.rerere.search  SearchResultItem me.rerere.search  
SearchService me.rerere.search  SearchServiceOptions me.rerere.search  
SerialName me.rerere.search  Serializable me.rerere.search  String me.rerere.search  Suppress me.rerere.search  T me.rerere.search  TAG me.rerere.search  
TavilyOptions me.rerere.search  TavilySearchService me.rerere.search  Text me.rerere.search  
TextButton me.rerere.search  
URLEncoder me.rerere.search  ZhipuDto me.rerere.search  ZhipuOptions me.rerere.search  ZhipuSearchResultDto me.rerere.search  ZhipuSearchService me.rerere.search  buildJsonObject me.rerere.search  	emptyList me.rerere.search  error me.rerere.search  
getOrThrow me.rerere.search  getValue me.rerere.search  
httpClient me.rerere.search  
isNotEmpty me.rerere.search  json me.rerere.search  lazy me.rerere.search  let me.rerere.search  map me.rerere.search  mapOf me.rerere.search  	onFailure me.rerere.search  println me.rerere.search  provideDelegate me.rerere.search  require me.rerere.search  runCatching me.rerere.search  stringResource me.rerere.search  success me.rerere.search  to me.rerere.search  toMediaType me.rerere.search  
toRequestBody me.rerere.search  withContext me.rerere.search  Dispatchers "me.rerere.search.BingSearchService  Jsoup "me.rerere.search.BingSearchService  R "me.rerere.search.BingSearchService  SearchResult "me.rerere.search.BingSearchService  SearchResultItem "me.rerere.search.BingSearchService  Text "me.rerere.search.BingSearchService  
URLEncoder "me.rerere.search.BingSearchService  
isNotEmpty "me.rerere.search.BingSearchService  map "me.rerere.search.BingSearchService  println "me.rerere.search.BingSearchService  require "me.rerere.search.BingSearchService  runCatching "me.rerere.search.BingSearchService  stringResource "me.rerere.search.BingSearchService  withContext "me.rerere.search.BingSearchService  
Composable !me.rerere.search.ExaSearchService  Dispatchers !me.rerere.search.ExaSearchService  Double !me.rerere.search.ExaSearchService  ExaContentsCost !me.rerere.search.ExaSearchService  ExaCostDollars !me.rerere.search.ExaSearchService  ExaData !me.rerere.search.ExaSearchService  	ExaResult !me.rerere.search.ExaSearchService  
ExaSearchCost !me.rerere.search.ExaSearchService  
JsonPrimitive !me.rerere.search.ExaSearchService  List !me.rerere.search.ExaSearchService  LocalUriHandler !me.rerere.search.ExaSearchService  R !me.rerere.search.ExaSearchService  Request !me.rerere.search.ExaSearchService  Result !me.rerere.search.ExaSearchService  SearchCommonOptions !me.rerere.search.ExaSearchService  SearchResult !me.rerere.search.ExaSearchService  SearchResultItem !me.rerere.search.ExaSearchService  SearchServiceOptions !me.rerere.search.ExaSearchService  
SerialName !me.rerere.search.ExaSearchService  Serializable !me.rerere.search.ExaSearchService  String !me.rerere.search.ExaSearchService  Text !me.rerere.search.ExaSearchService  
TextButton !me.rerere.search.ExaSearchService  buildJsonObject !me.rerere.search.ExaSearchService  error !me.rerere.search.ExaSearchService  
getOrThrow !me.rerere.search.ExaSearchService  
httpClient !me.rerere.search.ExaSearchService  json !me.rerere.search.ExaSearchService  map !me.rerere.search.ExaSearchService  	onFailure !me.rerere.search.ExaSearchService  println !me.rerere.search.ExaSearchService  runCatching !me.rerere.search.ExaSearchService  stringResource !me.rerere.search.ExaSearchService  success !me.rerere.search.ExaSearchService  toMediaType !me.rerere.search.ExaSearchService  
toRequestBody !me.rerere.search.ExaSearchService  withContext !me.rerere.search.ExaSearchService  Double 1me.rerere.search.ExaSearchService.ExaContentsCost  
SerialName 1me.rerere.search.ExaSearchService.ExaContentsCost  Double 0me.rerere.search.ExaSearchService.ExaCostDollars  ExaContentsCost 0me.rerere.search.ExaSearchService.ExaCostDollars  
ExaSearchCost 0me.rerere.search.ExaSearchService.ExaCostDollars  
SerialName 0me.rerere.search.ExaSearchService.ExaCostDollars  ExaCostDollars )me.rerere.search.ExaSearchService.ExaData  	ExaResult )me.rerere.search.ExaSearchService.ExaData  List )me.rerere.search.ExaSearchService.ExaData  
SerialName )me.rerere.search.ExaSearchService.ExaData  String )me.rerere.search.ExaSearchService.ExaData  results )me.rerere.search.ExaSearchService.ExaData  
SerialName +me.rerere.search.ExaSearchService.ExaResult  String +me.rerere.search.ExaSearchService.ExaResult  text +me.rerere.search.ExaSearchService.ExaResult  title +me.rerere.search.ExaSearchService.ExaResult  url +me.rerere.search.ExaSearchService.ExaResult  Double /me.rerere.search.ExaSearchService.ExaSearchCost  
SerialName /me.rerere.search.ExaSearchService.ExaSearchCost  
ExaOptions 6me.rerere.search.ExaSearchService.SearchServiceOptions  	bing_desc me.rerere.search.R.string  click_to_get_api_key me.rerere.search.R.string  Int $me.rerere.search.SearchCommonOptions  
resultSize $me.rerere.search.SearchCommonOptions  List me.rerere.search.SearchResult  SearchResultItem me.rerere.search.SearchResult  Serializable me.rerere.search.SearchResult  String me.rerere.search.SearchResult  String .me.rerere.search.SearchResult.SearchResultItem  BingSearchService me.rerere.search.SearchService  	Companion me.rerere.search.SearchService  
Composable me.rerere.search.SearchService  ExaSearchService me.rerere.search.SearchService  Json me.rerere.search.SearchService  OkHttpClient me.rerere.search.SearchService  Result me.rerere.search.SearchService  SearchCommonOptions me.rerere.search.SearchService  SearchResult me.rerere.search.SearchService  
SearchService me.rerere.search.SearchService  SearchServiceOptions me.rerere.search.SearchService  String me.rerere.search.SearchService  Suppress me.rerere.search.SearchService  T me.rerere.search.SearchService  TavilySearchService me.rerere.search.SearchService  ZhipuSearchService me.rerere.search.SearchService  getValue me.rerere.search.SearchService  lazy me.rerere.search.SearchService  provideDelegate me.rerere.search.SearchService  BingSearchService (me.rerere.search.SearchService.Companion  ExaSearchService (me.rerere.search.SearchService.Companion  Json (me.rerere.search.SearchService.Companion  OkHttpClient (me.rerere.search.SearchService.Companion  TavilySearchService (me.rerere.search.SearchService.Companion  ZhipuSearchService (me.rerere.search.SearchService.Companion  getValue (me.rerere.search.SearchService.Companion  
httpClient (me.rerere.search.SearchService.Companion  json (me.rerere.search.SearchService.Companion  lazy (me.rerere.search.SearchService.Companion  provideDelegate (me.rerere.search.SearchService.Companion  BingLocalOptions 3me.rerere.search.SearchService.SearchServiceOptions  
ExaOptions 3me.rerere.search.SearchService.SearchServiceOptions  
TavilyOptions 3me.rerere.search.SearchService.SearchServiceOptions  ZhipuOptions 3me.rerere.search.SearchService.SearchServiceOptions  BingLocalOptions %me.rerere.search.SearchServiceOptions  
ExaOptions %me.rerere.search.SearchServiceOptions  SearchServiceOptions %me.rerere.search.SearchServiceOptions  
SerialName %me.rerere.search.SearchServiceOptions  Serializable %me.rerere.search.SearchServiceOptions  String %me.rerere.search.SearchServiceOptions  
TavilyOptions %me.rerere.search.SearchServiceOptions  ZhipuOptions %me.rerere.search.SearchServiceOptions  mapOf %me.rerere.search.SearchServiceOptions  to %me.rerere.search.SearchServiceOptions  	Companion 6me.rerere.search.SearchServiceOptions.BingLocalOptions  BingLocalOptions /me.rerere.search.SearchServiceOptions.Companion  
ExaOptions /me.rerere.search.SearchServiceOptions.Companion  
TavilyOptions /me.rerere.search.SearchServiceOptions.Companion  ZhipuOptions /me.rerere.search.SearchServiceOptions.Companion  mapOf /me.rerere.search.SearchServiceOptions.Companion  to /me.rerere.search.SearchServiceOptions.Companion  	Companion 0me.rerere.search.SearchServiceOptions.ExaOptions  String 0me.rerere.search.SearchServiceOptions.ExaOptions  apiKey 0me.rerere.search.SearchServiceOptions.ExaOptions  	Companion 3me.rerere.search.SearchServiceOptions.TavilyOptions  String 3me.rerere.search.SearchServiceOptions.TavilyOptions  apiKey 3me.rerere.search.SearchServiceOptions.TavilyOptions  	Companion 2me.rerere.search.SearchServiceOptions.ZhipuOptions  String 2me.rerere.search.SearchServiceOptions.ZhipuOptions  apiKey 2me.rerere.search.SearchServiceOptions.ZhipuOptions  
Composable $me.rerere.search.TavilySearchService  Dispatchers $me.rerere.search.TavilySearchService  Double $me.rerere.search.TavilySearchService  
JsonPrimitive $me.rerere.search.TavilySearchService  List $me.rerere.search.TavilySearchService  LocalUriHandler $me.rerere.search.TavilySearchService  Request $me.rerere.search.TavilySearchService  Result $me.rerere.search.TavilySearchService  
ResultItem $me.rerere.search.TavilySearchService  SearchCommonOptions $me.rerere.search.TavilySearchService  SearchResponse $me.rerere.search.TavilySearchService  SearchResult $me.rerere.search.TavilySearchService  SearchResultItem $me.rerere.search.TavilySearchService  SearchServiceOptions $me.rerere.search.TavilySearchService  Serializable $me.rerere.search.TavilySearchService  String $me.rerere.search.TavilySearchService  Text $me.rerere.search.TavilySearchService  
TextButton $me.rerere.search.TavilySearchService  buildJsonObject $me.rerere.search.TavilySearchService  	emptyList $me.rerere.search.TavilySearchService  error $me.rerere.search.TavilySearchService  
httpClient $me.rerere.search.TavilySearchService  json $me.rerere.search.TavilySearchService  let $me.rerere.search.TavilySearchService  map $me.rerere.search.TavilySearchService  runCatching $me.rerere.search.TavilySearchService  success $me.rerere.search.TavilySearchService  
toRequestBody $me.rerere.search.TavilySearchService  withContext $me.rerere.search.TavilySearchService  Double /me.rerere.search.TavilySearchService.ResultItem  String /me.rerere.search.TavilySearchService.ResultItem  content /me.rerere.search.TavilySearchService.ResultItem  title /me.rerere.search.TavilySearchService.ResultItem  url /me.rerere.search.TavilySearchService.ResultItem  List 3me.rerere.search.TavilySearchService.SearchResponse  
ResultItem 3me.rerere.search.TavilySearchService.SearchResponse  String 3me.rerere.search.TavilySearchService.SearchResponse  	emptyList 3me.rerere.search.TavilySearchService.SearchResponse  results 3me.rerere.search.TavilySearchService.SearchResponse  	emptyList =me.rerere.search.TavilySearchService.SearchResponse.Companion  
TavilyOptions 9me.rerere.search.TavilySearchService.SearchServiceOptions  
Composable #me.rerere.search.ZhipuSearchService  Dispatchers #me.rerere.search.ZhipuSearchService  
JsonPrimitive #me.rerere.search.ZhipuSearchService  List #me.rerere.search.ZhipuSearchService  LocalUriHandler #me.rerere.search.ZhipuSearchService  Request #me.rerere.search.ZhipuSearchService  Result #me.rerere.search.ZhipuSearchService  SearchCommonOptions #me.rerere.search.ZhipuSearchService  SearchResult #me.rerere.search.ZhipuSearchService  SearchResultItem #me.rerere.search.ZhipuSearchService  SearchServiceOptions #me.rerere.search.ZhipuSearchService  
SerialName #me.rerere.search.ZhipuSearchService  Serializable #me.rerere.search.ZhipuSearchService  String #me.rerere.search.ZhipuSearchService  Text #me.rerere.search.ZhipuSearchService  
TextButton #me.rerere.search.ZhipuSearchService  ZhipuDto #me.rerere.search.ZhipuSearchService  ZhipuSearchResultDto #me.rerere.search.ZhipuSearchService  buildJsonObject #me.rerere.search.ZhipuSearchService  error #me.rerere.search.ZhipuSearchService  
getOrThrow #me.rerere.search.ZhipuSearchService  
httpClient #me.rerere.search.ZhipuSearchService  json #me.rerere.search.ZhipuSearchService  map #me.rerere.search.ZhipuSearchService  	onFailure #me.rerere.search.ZhipuSearchService  println #me.rerere.search.ZhipuSearchService  runCatching #me.rerere.search.ZhipuSearchService  success #me.rerere.search.ZhipuSearchService  toMediaType #me.rerere.search.ZhipuSearchService  
toRequestBody #me.rerere.search.ZhipuSearchService  withContext #me.rerere.search.ZhipuSearchService  ZhipuOptions 8me.rerere.search.ZhipuSearchService.SearchServiceOptions  List ,me.rerere.search.ZhipuSearchService.ZhipuDto  
SerialName ,me.rerere.search.ZhipuSearchService.ZhipuDto  ZhipuSearchResultDto ,me.rerere.search.ZhipuSearchService.ZhipuDto  searchResult ,me.rerere.search.ZhipuSearchService.ZhipuDto  
SerialName 8me.rerere.search.ZhipuSearchService.ZhipuSearchResultDto  String 8me.rerere.search.ZhipuSearchService.ZhipuSearchResultDto  content 8me.rerere.search.ZhipuSearchService.ZhipuSearchResultDto  link 8me.rerere.search.ZhipuSearchService.ZhipuSearchResultDto  title 8me.rerere.search.ZhipuSearchService.ZhipuSearchResultDto  Call okhttp3  	MediaType okhttp3  OkHttpClient okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  ResponseBody okhttp3  execute okhttp3.Call  toMediaType okhttp3.MediaType.Companion  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  newCall okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  Builder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  post okhttp3.Request.Builder  url okhttp3.Request.Builder  
toRequestBody okhttp3.RequestBody.Companion  body okhttp3.Response  code okhttp3.Response  isSuccessful okhttp3.Response  string okhttp3.ResponseBody  
Connection 	org.jsoup  Jsoup 	org.jsoup  cookie org.jsoup.Connection  get org.jsoup.Connection  header org.jsoup.Connection  referrer org.jsoup.Connection  timeout org.jsoup.Connection  	userAgent org.jsoup.Connection  connect org.jsoup.Jsoup  Document org.jsoup.nodes  Element org.jsoup.nodes  select org.jsoup.nodes.Document  select org.jsoup.nodes.Element  Elements org.jsoup.select  attr org.jsoup.select.Elements  map org.jsoup.select.Elements  text org.jsoup.select.Elements                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              